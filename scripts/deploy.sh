#!/bin/bash

# Custom deployment script for Cloudflare Workers Sites
# This bypasses the dashboard's forced use of wrangler versions upload

echo "🚀 Starting Cloudflare Workers deployment..."

# Check if wrangler is available
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler not found. Installing..."
    npm install -g wrangler
fi

# Build the project (using CI build to skip image optimization)
echo "📦 Building project..."
npm run build:ci

# Deploy using the correct wrangler command
echo "🌐 Deploying to Cloudflare Workers..."
npx wrangler deploy --legacy-env false

echo "✅ Deployment complete!"
echo ""
echo "💡 If this script works but the dashboard deployment fails,"
echo "   it means Cloudflare dashboard is forcing the wrong command."
echo "   Use this script for deployments: npm run deploy:manual"
