import { getAssetFromKV } from '@cloudflare/kv-asset-handler'

const DEBUG = false

addEventListener('fetch', event => {
  event.respondWith(handleEvent(event).catch(err => {
    if (DEBUG) {
      return new Response(err.stack, { status: 500 })
    }
    return new Response('Internal Error', { status: 500 })
  }))
})

async function handleEvent(event) {
  const { request } = event
  const url = new URL(request.url)

  // Try to serve static assets first
  try {
    const page = await getAssetFromKV(event, DEBUG ? { cacheControl: { bypassCache: true } } : {})
    const response = new Response(page.body, page)

    // Add security headers
    response.headers.set('X-XSS-Protection', '1; mode=block')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')

    // Cache control for different file types
    const pathname = url.pathname
    
    // Long cache for optimized images and assets
    if (pathname.includes('/img/') && (pathname.includes('.webp') || pathname.includes('-optimized.') || pathname.includes('-low.'))) {
      response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
    }
    // Standard cache for regular images and assets
    else if (pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2)$/)) {
      response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
    }
    // No cache for HTML files to ensure fresh content
    else if (pathname.endsWith('.html') || pathname === '/' || !pathname.includes('.')) {
      response.headers.set('Cache-Control', 'public, max-age=0, must-revalidate')
    }

    return response
  } catch (e) {
    // If asset not found, check if it's a file request that should return 404
    const pathname = url.pathname

    // Don't serve index.html for actual file requests (except .html routes)
    if (pathname.includes('.') &&
        !pathname.endsWith('.html') &&
        !pathname.endsWith('/') &&
        pathname !== '/index.html') {
      return new Response('Not Found', { status: 404 })
    }

    // For SPA routes or missing pages, serve index.html
    try {
      const notFound = await getAssetFromKV(event, {
        mapRequestToAsset: req => new Request(`${new URL(req.url).origin}/index.html`, req),
      })
      return new Response(notFound.body, {
        ...notFound,
        status: 200,
        headers: {
          ...notFound.headers,
          'Content-Type': 'text/html; charset=utf-8',
          'Cache-Control': 'public, max-age=0, must-revalidate'
        }
      })
    } catch (error) {
      return new Response('Not Found', { status: 404 })
    }
  }
}
