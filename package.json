{"name": "martindoubravsky-com", "version": "1.0.0", "description": "<PERSON>'s personal website with performance optimizations", "main": "index.html", "scripts": {"optimize-images": "./scripts/optimize-images.sh", "optimize-image": "./scripts/optimize-images.sh", "build": "npm run optimize-images", "build:ci": "echo 'Using pre-optimized images for CI deployment'", "build:fast": "echo 'Skipping optimization for fast build'", "deploy": "npm run build && npx wrangler deploy --legacy-env false", "deploy:manual": "./scripts/deploy.sh", "cf:dev": "npx wrangler dev", "preview": "npx serve .", "start": "npx serve ."}, "devDependencies": {"@cloudflare/kv-asset-handler": "^0.4.0", "wrangler": "^4.19.1", "serve": "^14.2.1"}, "keywords": ["portfolio", "ux-design", "performance", "cloudflare-workers"], "author": "<PERSON>", "license": "MIT"}