# Cache Headers for Performance Optimization

# Long cache for optimized images
/img/*.webp
  Cache-Control: public, max-age=31536000, immutable

/img/*-optimized.*
  Cache-Control: public, max-age=31536000, immutable

/img/*-low.jpg
  Cache-Control: public, max-age=31536000, immutable

# Standard cache for regular assets
/img/*
  Cache-Control: public, max-age=2592000

/js/*
  Cache-Control: public, max-age=31536000, immutable

/css/*
  Cache-Control: public, max-age=31536000, immutable

# Font files
*.woff
  Cache-Control: public, max-age=31536000, immutable

*.woff2
  Cache-Control: public, max-age=31536000, immutable

# No cache for HTML files
/*.html
  Cache-Control: public, max-age=0, must-revalidate

/
  Cache-Control: public, max-age=0, must-revalidate

# Security headers for all files
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
