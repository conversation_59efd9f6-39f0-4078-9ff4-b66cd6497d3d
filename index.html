<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title><PERSON> • Product & UX Design Lead</title>
    <meta name="title" content="<PERSON> • Product & UX Design Lead">
    <meta name="description"
        content="Helping global businesses create meaningful digital solutions. Specializing in UX/UI design, product strategy, and digital transformation since 2006.">
    <meta name="author" content="<PERSON>">
    <meta name="keywords"
        content="UX design, UI design, product design, digital strategy, Prague, remote design, global design consultant">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://martindoubravsky.com/">
    <meta property="og:title" content="<PERSON> • Product & UX Design Lead">
    <meta property="og:description"
        content="Helping global businesses create meaningful digital solutions. 17 years of experience in UX/UI design and product strategy.">
    <meta property="og:image" content="img/martindoubravsky-og.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://martindoubravsky.com/">
    <meta property="twitter:title" content="Martin Doubravský • Product & UX Design Lead">
    <meta property="twitter:description"
        content="Helping global businesses create meaningful digital solutions. 17 years of experience in UX/UI design and product strategy.">
    <meta property="twitter:image" content="img/martindoubravsky-og.png">

    <link href="cdc29e94b4caf01085e87de_avatar-favicon.png" rel="shortcut icon" type="image/x-icon" />
    <link href="5cdc2a66d7274d6d7fc61a2c_avatar-256.png" rel="apple-touch-icon" />

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    spacing: {
                        '80vh': '80vh',
                    },
                    fontSize: {
                        '7xl': ['5rem', '1.1'],
                        '8xl': ['6.5rem', '1.1'],
                    }
                }
            }
        }
    </script>
    <!-- Performance Optimized Font Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700;800&family=Inter:wght@400;500;600&display=swap"
        rel="stylesheet">

    <!-- Critical Resource Preloading -->
    <link rel="preload" href="img/jvzXHNbTB6wGm1VyXrtpo0NF0SL2D0M4o8pBKGCF-low.jpg" as="image">
    <link rel="preload" href="img/5cf549cc73a6231e45b94e50_IMG_5923-low.jpg" as="image">

    <!-- External Resources -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />

    <!-- Optimized Image Loading Script -->
    <script src="js/optimized-images.js" defer></script>


    <style>
        /* Font Loading Optimization */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            font-display: swap;
        }

        h1,
        h2,
        h3 {
            font-family: 'Plus Jakarta Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            font-weight: 800;
            font-display: swap;
        }

        /* Font loading states */
        .font-loading { visibility: hidden; }
        .fonts-loaded .font-loading { visibility: visible; }

        /* Progressive Image Loading */
        .progressive-image {
            transition: filter 0.5s ease, transform 0.5s ease;
        }
        .progressive-image.loading {
            filter: blur(5px);
            transform: scale(1.02);
        }
        .progressive-image.loaded {
            filter: none;
            transform: scale(1);
        }

        .testimonial-slide {
            width: 100%;
            flex-shrink: 0;
            transition: transform 0.5s ease;
        }

        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #E5E7EB;
            transition: all 0.3s ease;
        }

        .dot.active {
            background-color: #ffbc37;
            width: 24px;
            border-radius: 4px;
        }

        .hero-height {
            height: 80vh;
            min-height: 600px;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ffffff;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loading-bar-container {
            width: 300px;
            height: 4px;
            background-color: #f1f5f9;
            overflow: hidden;
            position: relative;
        }

        .loading-bar {
            position: absolute;
            width: 50%;
            height: 100%;
            background-color: #ffbc37;
            left: 50%;
            transform-origin: left;
            transform: scaleX(0);
        }

        .loading-bar-left {
            position: absolute;
            width: 50%;
            height: 100%;
            background-color: #ffbc37;
            right: 50%;
            transform-origin: right;
            transform: scaleX(0);
        }

        .hide-loader {
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
            pointer-events: none;
        }

        /* Custom pagination */
        .swiper-pagination {
            position: relative;
            margin-top: 3rem;
        }

        .swiper-pagination-bullet {
            width: 8px;
            height: 8px;
            background: #E5E7EB;
            opacity: 1;
            transition: all 0.3s ease;
        }

        .swiper-pagination-bullet-active {
            width: 24px;
            border-radius: 4px;
            background: #ffbc37;
        }

        /* Slide styles */
        .testimonial-slide {
            height: auto;
        }

        @media (max-width: 768px) {
            .swiper-slide {
                opacity: 0.5;
                transition: opacity 0.3s ease;
            }

            .swiper-slide-active {
                opacity: 1;
            }
        }

        /* Hide default Swiper navigation */
        .swiper-button-next::after,
        .swiper-button-prev::after {
            display: none;
        }

        /* Custom navigation styles */
        .custom-swiper-button {
            display: none;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }

        .custom-swiper-button-prev {
            left: -80px;
            /* Adjust this value as needed */
        }

        .custom-swiper-button-next {
            right: -80px;
            /* Adjust this value as needed */
        }

        @media (min-width: 768px) {
            .custom-swiper-button {
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <!-- Font Loading Detection -->
    <script>
        if ('fonts' in document) {
            document.fonts.ready.then(() => {
                document.documentElement.classList.add('fonts-loaded');
            });
        } else {
            setTimeout(() => {
                document.documentElement.classList.add('fonts-loaded');
            }, 100);
        }
    </script>
</head>

<body class="bg-slate-50 text-gray-900">
    <div class="loading-overlay">
        <div class="loading-bar-container">
            <div class="loading-bar-left"></div>
            <div class="loading-bar"></div>
        </div>
    </div>
    <!-- Navbar -->
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-slate-50 z-50">
        <div class="container mx-auto px-6">
            <div class="flex justify-between items-center py-6">
                <a href="#" class="text-2xl font-semibold">Martin Doubravský</a>

                <!-- Desktop Menu -->
                <div class="hidden md:flex space-x-12 items-center">
                    <a href="http://upwork.com/fl/martindoubravsky" class="hover:text-[#ffbc37] transition-colors">Work</a>
                    <a href="#about" class="hover:text-[#ffbc37] transition-colors">About</a>
                    <a href="#testimonials" class="hover:text-[#ffbc37] transition-colors">Testimonials</a>
                    <a href="#contact"
                        class="bg-[#ffbc37] text-gray-900 px-8 py-3 rounded-full hover:bg-[#e6a832] transition-colors font-medium">Contact</a>
                </div>

                <!-- Mobile Menu Button -->
                <button id="mobileMenuButton" class="md:hidden p-2" type="button" aria-label="Toggle menu">
                    <i class="fas fa-bars text-2xl"></i>
                </button>
            </div>

            <!-- Mobile Menu Panel -->
            <div id="mobileMenuPanel" class="hidden md:hidden">
                <div class="py-6 space-y-6 text-lg">
                    <a href="http://upwork.com/fl/martindoubravsky" target="_blank" class="block hover:text-[#ffbc37] transition-colors">Work</a>
                    <a href="#about" class="block hover:text-[#ffbc37] transition-colors">About</a>
                    <a href="#testimonials" class="block hover:text-[#ffbc37] transition-colors">Testimonials</a>
                    <a href="#contact"
                        class="block bg-[#ffbc37] text-gray-900 px-8 py-3 rounded-full hover:bg-[#e6a832] transition-colors font-medium text-center">Contact</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header class="min-h-[80vh] flex items-center pt-24 md:pt-0 bg-slate-50">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div>
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-6 leading-tight">
                        Turning complex ideas into elegant digital experiences
                    </h1>
                    <p class="text-lg md:text-xl lg:text-2xl text-gray-600 mb-8 md:pr-12">
                        Product & UX Design Lead with 17 years of experience helping global businesses create
                        meaningful digital solutions.
                    </p>
                    <div class="flex flex-wrap gap-4">
                        <a href="#contact"
                            class="bg-[#ffbc37] text-gray-900 px-8 py-4 rounded-lg hover:bg-[#e6a832] transition-colors text-lg font-medium">
                            Start a Project
                        </a>
                        <a href="http://upwork.com/fl/martindoubravsky" target="_blank"
                            class="border-2 border-gray-900 px-8 py-4 rounded-lg hover:bg-white transition-colors text-lg font-medium">
                            View Work
                        </a>
                    </div>
                </div>
                <div class="flex justify-center md:justify-end">
                    <div class="w-64 h-64 lg:w-[500px] lg:h-[500px] relative">
                        <img data-src="img/jvzXHNbTB6wGm1VyXrtpo0NF0SL2D0M4o8pBKGCF.png"
                             data-priority="true"
                             alt="Martin Doubravský"
                             class="rounded-full w-full h-full object-cover shadow-lg progressive-image">
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- Updated Clients Section -->
    <section class="py-24 md:py-32 bg-white">
        <div class="container mx-auto px-6">
            <!-- <p class="text-center text-gray-600 mb-24 text-xl">Trusted by innovative companies worldwide</p> -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-16 mb-24">
                <div class="flex items-center justify-center p-8">
                    <img src="img/clients/5cff8c328fcb4e2f8771ebde_Allianz.svg" alt="Client Logo"
                        class="max-w-[160px] opacity-60">
                </div>
                <div class="flex items-center justify-center p-8">
                    <img src="img/clients/61fd3b5fb4af65192dbec2c4_creativedock.svg" alt="Client Logo"
                        class="max-w-[160px] opacity-60">
                </div>
                <div class="flex items-center justify-center p-8">
                    <img src="img/clients/5cff8c405a6c7750973e9f55_Infor_logo.svg" alt="Client Logo"
                        class="max-w-[160px] opacity-60">
                </div>
                <div class="flex items-center justify-center p-8">
                    <img src="img/clients/<EMAIL>" alt="Client Logo"
                        class="max-w-[160px] opacity-60">
                </div>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-16">
                <div class="flex items-center justify-center p-8">
                    <img src="img/clients/60180e6ae976888d9b23fa40_screensteps-logo-bw.png" alt="Client Logo"
                        class="max-w-[160px] opacity-60">
                </div>
                <div class="flex items-center justify-center p-8">
                    <img src="img/clients/5cff8c57b5187f3845a1349a_vendo_services_the_ai_biller_logo.svg"
                        alt="Client Logo" class="max-w-[160px] opacity-60">
                </div>
                <div class="flex items-center justify-center p-8">
                    <img src="img/clients/<EMAIL>" alt="Client Logo"
                        class="max-w-[160px] opacity-60">
                </div>
                <div class="flex items-center justify-center p-8">
                    <img src="img/clients/<EMAIL>" alt="Client Logo"
                        class="max-w-[160px] opacity-60">
                </div>
            </div>
        </div>
    </section>

    <!-- Global Reach Section -->
    <section class="py-24 md:py-32 bg-gray-900 text-white">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl md:text-5xl font-extrabold mb-8 text-center">Clients All Over the World</h2>
            <p class="text-2xl text-center mb-16 text-gray-400">Working remotely since 2006 with clients across all time
                zones</p>
            <div class="relative rounded-2xl overflow-hidden">
                <img data-src="img/<EMAIL>"
                    alt="World Map showing Martin's client locations"
                    class="w-full opacity-90 progressive-image">
            </div>
        </div>
    </section>

    <!-- How I Work Section -->
    <section class="py-24 md:py-32 bg-slate-50">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl md:text-5xl font-bold mb-16 text-center">How I Work</h2>
            <div class="grid md:grid-cols-3 gap-8">
                <div
                    class="bg-white p-12 rounded-2xl shadow-[0_4px_24px_rgba(0,0,0,0.08)] hover:shadow-[0_4px_32px_rgba(0,0,0,0.12)] transition-shadow">
                    <div class="w-16 h-16 bg-[#ffbc37]/10 rounded-xl flex items-center justify-center mb-8">
                        <i class="fas fa-comments text-[#ffbc37] text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-6">Clear Communication</h3>
                    <p class="text-xl text-gray-600">I keep things simple and direct. After 17 years of remote work,
                        I've learned that good communication means being clear, responsive, and honest about what's
                        possible and what isn't.</p>
                </div>

                <div
                    class="bg-white p-12 rounded-2xl shadow-[0_4px_24px_rgba(0,0,0,0.08)] hover:shadow-[0_4px_32px_rgba(0,0,0,0.12)] transition-shadow">
                    <div class="w-16 h-16 bg-[#ffbc37]/10 rounded-xl flex items-center justify-center mb-8">
                        <i class="fas fa-lightbulb text-[#ffbc37] text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-6">Problem Solving</h3>
                    <p class="text-xl text-gray-600">I focus on understanding the real problem first. Sometimes that
                        means asking uncomfortable questions, but it leads to solutions that actually work, not just
                        quick fixes.</p>
                </div>

                <div
                    class="bg-white p-12 rounded-2xl shadow-[0_4px_24px_rgba(0,0,0,0.08)] hover:shadow-[0_4px_32px_rgba(0,0,0,0.12)] transition-shadow">
                    <div class="w-16 h-16 bg-[#ffbc37]/10 rounded-xl flex items-center justify-center mb-8">
                        <i class="fas fa-check-circle text-[#ffbc37] text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-6">I Keep My Word</h3>
                    <p class="text-xl text-gray-600">I do what I say I'll do, when I say I'll do it. If something's
                        going to take longer or needs a different approach, you'll know right away. No mysteries, no
                        surprises. <b>My clients appreciate it.</b></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-24 md:py-32 bg-slate-50 relative">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl md:text-5xl font-bold mb-16 text-center">What clients say</h2>

            <div class="relative max-w-4xl mx-auto">
                <!-- Navigation arrows now outside any overflow:hidden container -->
                <button id="prevBtn"
                    class="absolute -left-20 top-1/2 -translate-y-1/2 p-6 rounded-full bg-white shadow-lg hover:bg-[#ffbc37] transition-colors text-gray-900 hidden md:block">
                    <i class="fas fa-chevron-left text-xl"></i>
                </button>
                <button id="nextBtn"
                    class="absolute -right-20 top-1/2 -translate-y-1/2 p-6 rounded-full bg-white shadow-lg hover:bg-[#ffbc37] transition-colors text-gray-900 hidden md:block">
                    <i class="fas fa-chevron-right text-xl"></i>
                </button>

                <div class="swiper testimonialSwiper">
                    <div class="swiper-wrapper">
                        <!-- Slides here -->
                    </div>

                    <div class="swiper-pagination mt-12"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-24 md:py-32 bg-white">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-2 gap-16 items-start">
                <div class="relative">
                    <img data-src="img/5cf549cc73a6231e45b94e50_IMG_5923.jpg"
                         alt="Martin Doubravský"
                         class="rounded-2xl shadow-lg w-full max-w-md mx-auto progressive-image">
                </div>
                <div>
                    <h2 class="text-4xl md:text-5xl font-bold mb-12">My Story</h2>
                    <div class="space-y-8">
                        <p class="text-xl text-gray-600">Based in Prague, Czechia, I've been crafting digital
                            experiences for nearly two decades. My approach combines strategic thinking with creative
                            problem-solving, always focusing on delivering meaningful solutions that truly serve
                            businesses and their clients.</p>
                        <p class="text-xl text-gray-600">I extract thoughts from clients' heads, contemplate ideas,
                            sketch solutions, and create strategic recommendations that align with business goals while
                            understanding customer needs.</p>
                        <p class="text-xl text-gray-600">My success is built on creative and innovative thinking, quick
                            learning capabilities, and taking responsibility in everything I&nbsp;do.</p>
                        <p class="text-xl text-gray-600">And of course, I've integrated AI into my daily workflow, making it indispensable like pen and paper. In fact, this whole page was done with AI – despite being a UX designer, I haven't designed it; despite loving to code, I haven't written it. And yet, it looks how I want and does what I need.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Client List Section -->
    <section class="py-24 md:py-32 bg-white">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl md:text-5xl font-bold mb-16 text-center">Partial Client List</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-xl">
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Allianz</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Automotive Data Technik, AG</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Centrum Holdings</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Creative Dock</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Cultuzz Digital Media GmbH</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">DNS</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Easytask</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Epic agency</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Etnetera</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Foneco</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Hourly, Inc.</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">IMKG Consulting</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Infor</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Madeo Interactive</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Mangoweb</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Mlada Fronta</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Narvar Inc.</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Periscope Media</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Playboy</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">RealDeveloper</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">RocketTheme</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Sable Money, Inc.</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Screensteps, LLC</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Sharkey Media</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Speedflowers</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Tego Interactive</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Vendo Services GMBH</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">WeddingWire, Inc.</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Worry Free Labs</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">Yogis Anonymous</div>
                <div class="p-4 text-center hover:text-[#ffbc37] transition-colors">YouHue Inc.</div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-24 md:py-32 bg-gray-900 text-white">
        <div class="container mx-auto px-6">
            <div class="max-w-3xl mx-auto">
                <h2 class="text-4xl md:text-5xl font-extrabold mb-8 text-center">Start Your Project</h2>
                <p class="text-2xl text-gray-400 mb-16 text-center">Let's discuss how I can help turn your ideas into
                    reality.</p>

                <form class="space-y-10" id="contactForm">
                    <!-- Add these hidden fields -->
                    <input type="hidden" name="_subject" value="New prospect from martindoubravsky.com">
                    <input type="hidden" name="_template" value="table">
                    <input type="hidden" name="_captcha" value="false">
                    <input type="hidden" name="_next" value="https://martindoubravsky.com/?thanks">

                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <label class="block mb-3 text-lg font-medium">Name *</label>
                            <input type="text" name="from_name" required
                                class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg">
                        </div>
                        <div>
                            <label class="block mb-3 text-lg font-medium">Company</label>
                            <input type="text" name="company"
                                class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg">
                        </div>
                    </div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <label class="block mb-3 text-lg font-medium">Email *</label>
                            <input type="email" name="from_email" required
                                class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg">
                        </div>
                        <div>
                            <label class="block mb-3 text-lg font-medium">Location</label>
                            <input type="text" name="location"
                                class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg">
                        </div>
                    </div>

                    <div>
                        <label class="block mb-3 text-lg font-medium">What's the problem you need to solve? *</label>
                        <textarea name="problem" required rows="3"
                            class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg"
                            placeholder="Describe the main challenge or pain point you're facing"></textarea>
                    </div>

                    <div>
                        <label class="block mb-3 text-lg font-medium">What needs to be done to achieve your goal?
                            *</label>
                        <textarea name="goals" required rows="3"
                            class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg"
                            placeholder="List the key objectives or deliverables needed"></textarea>
                    </div>

                    <div>
                        <label class="block mb-3 text-lg font-medium">Why not do nothing at all instead? *</label>
                        <textarea name="why_not" required rows="3"
                            class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg"
                            placeholder="What are the consequences of not addressing this problem?"></textarea>
                    </div>

                    <div>
                        <label class="block mb-3 text-lg font-medium">What does success look like? How will you measure
                            it? *</label>
                        <textarea name="success" required rows="3"
                            class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg"
                            placeholder="Define your success metrics and expected outcomes"></textarea>
                    </div>

                    <div>
                        <label class="block mb-3 text-lg font-medium">Do you already have some assets?</label>
                        <textarea name="assets" rows="3"
                            class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg"
                            placeholder="Brand identity, color palette, brand voice, articles, posts, videos, or other assets?"></textarea>
                    </div>

                    <div>
                        <label class="block mb-3 text-lg font-medium">Complete the sentence "My solution is the ONLY one
                            that..." *</label>
                        <input type="text" name="only" required
                            class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg"
                            placeholder="What makes your solution unique?">
                    </div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <label class="block mb-3 text-lg font-medium">When do you need it done? *</label>
                            <input type="text" name="when" required
                                class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg"
                                placeholder="Your desired timeline">
                        </div>
                        <div>
                            <label class="block mb-3 text-lg font-medium">What's your budget? *</label>
                            <input type="text" name="budget" required
                                class="w-full px-6 py-4 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:border-[#ffbc37] focus:ring-1 focus:ring-[#ffbc37] outline-none transition-colors text-lg"
                                placeholder="Your budget range">
                        </div>
                    </div>

                    <button type="submit"
                        class="w-full bg-[#ffbc37] text-gray-900 px-8 py-5 rounded-lg hover:bg-[#e6a832] transition-colors text-xl font-medium">
                        Send
                    </button>
                </form>
            </div>
        </div>
    </section>
    <!-- Footer -->
    <footer class="py-16 bg-gray-900 text-gray-400 border-t border-gray-800">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row justify-between items-center gap-12">
                <div>
                    <p class="text-white text-xl mb-4">Martin Doubravský</p>
                    <p class="text-lg">Josefa Hory 4081/25, 46604 Jablonec nad Nisou, Czechia</p>
                    <p class="text-lg">ID 74760483 • VAT Registered</p>
                    <p class="text-lg"><a class="text-xl text-white hover:text-[#ffbc37] transition-colors" href="https://martindoubravsky.cz/">What else do I create?</a></p>
                </div>
                <div class="flex flex-col items-end gap-6">
                    <div class="flex flex-col items-end gap-3">
                        <a href="mailto:<EMAIL>"
                            class="text-xl text-white hover:text-[#ffbc37] transition-colors">
                            <EMAIL>
                        </a>
                        <a href="https://wa.me/420607748413" target="_blank" rel="noopener noreferrer"
                            class="text-xl hover:text-[#ffbc37] transition-colors">
                            <i class="fab fa-whatsapp mr-2"></i>+420 607 748 413
                        </a>
                    </div>
                    <div class="flex gap-6 text-xl">
                        <a href="http://upwork.com/fl/martindoubravsky" target="_blank" rel="noopener noreferrer"
                            class="hover:text-[#ffbc37] transition-colors">
                            <i class="fab fa-upwork"></i>
                        </a>
                        <a href="http://linkedin.com/in/martindoubravsky/" target="_blank" rel="noopener noreferrer"
                            class="hover:text-[#ffbc37] transition-colors">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="http://instagram.com/martin_doubravsky/" target="_blank" rel="noopener noreferrer"
                            class="hover:text-[#ffbc37] transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="http://facebook.com/martindoubravsky" target="_blank" rel="noopener noreferrer"
                            class="hover:text-[#ffbc37] transition-colors">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="http://twitter.com/martindoub" target="_blank" rel="noopener noreferrer"
                            class="hover:text-[#ffbc37] transition-colors">
                            <i class="fab fa-x-twitter"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
    </footer>

    <div id="thankYouMessage" class="fixed inset-0 z-50 bg-white bg-opacity-95 backdrop-blur-sm hidden">
        <div class="container mx-auto px-6 h-full flex items-center justify-center">
            <div class="max-w-2xl text-center">
                <div class="mb-8">
                    <!-- Success checkmark animation -->
                    <div class="w-20 h-20 mx-auto bg-[#ffbc37] rounded-full flex items-center justify-center mb-8">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7">
                            </path>
                        </svg>
                    </div>
                    <h2 class="text-4xl font-bold mb-4">Thank You!</h2>
                    <p class="text-xl text-gray-600 mb-8">I've received your message and will get back to you shortly.
                    </p>
                </div>
                <button onclick="closeThankYou()"
                    class="bg-[#ffbc37] text-gray-900 px-8 py-4 rounded-lg hover:bg-[#e6a832] transition-colors text-lg font-medium">
                    Back to Homepage
                </button>
            </div>
        </div>
    </div>

    <script>
        // Testimonials Data
        const testimonials = [
            {
                text: "I can recommend Martin as a person who has profound knowledge and great abilities of advanced business solutions. He was well respected in our department and everyone appreciated working with him. Result driven, experienced and efficient team player. Deliver results and move on. Besides being a joy to work with, Martin is a take-charge person who is able to present creative solutions to complex problems and communicate the benefits to the company. I thoroughly enjoyed dealing with him at Creative Dock.",
                author: "Catalin Militaru",
                role: "Product Lead, Fairo | Creative Dock",
                image: "img/testimonials/6239a3250ac5692fd11ef3f5_1634543389678-p-500.jpeg"
            },
            {
                text: "I was working with Martin on a project for an intensive few months - it was always a pleasure to work with him and I believe we brought a lot of value to customers because of him. Martin is relentless customer voice advocate and his creative ideas were always very welcome. Hope we will have the oportunity to stay in touch.",
                author: "Michal Studnicka",
                role: "Product Owner, Fairo | Creative Dock",
                image: "img/testimonials/6230731ec06ec277a36b4165_126AA891-6E3F-4F35-A79D-A933F169B05B.jpeg"
            },
            {
                text: "I had the opportunity to work with Martin in a rush environment. He was always very flexible, very easily caught the problem, constantly coming up with great ideas focused on a customer's needs. Huge skills in UX/UI, great out of box thinking. Thanks for a nice cooperation, Martin!",
                author: "Michal Beran",
                role: "Product Owner, Fairo | Creative Dock",
                image: "img/testimonials/6220fdc96e2780ed7bc61c1c_1565041138186-p-500.jpeg"
            },
            {
                text: "It was a pleasure to cooperate with Martin. He combines vision and creative outlook on UX/UI with realism and ability to seek meaningful compromise with other stakeholders. Such combination is rare to find!",
                author: "Prokop Houda",
                role: "IT Business Analyst, Fairo | Creative Dock",
                image: "img/testimonials/6214c8de83467e40d4d5eead_1517484360743.jpeg"
            },
            {
                text: "We would highly recommend Martin. Excellent work taking ownership of a problem and seeing it through. His communication is top-notch.",
                author: "Sterling Whitley",
                role: "Narvar, Series-C startup from San Francisco",
                image: ""
            },
            {
                text: "I have worked in the digital industry for over 13 years and I can honestly say Martin is one of the best. Apart from his design and UX skills, his greatest asset is clear communication. It was effortless working with him remotely and I will certainly be looking to work with him again very soon. Thanks for your great work Martin.",
                author: "James Chivers",
                role: "Digital Product Manager, Founder of Real Developer, Australia",
                image: "img/testimonials/5cdd71d5ee894c182aaeff40_jameschivers.png"
            },
            {
                text: "Martin's ability to think and execute for the benefit of the client and ultimately, the user, is immediately apparent. His work is excellent and shows clearly that he cares about the end result. His communication skills are fantastic and you cannot overstate the importance of that quality in a developer. I look forward to continuing work with Martin!",
                author: "Dorian Cheah",
                role: "Co-founder of Yogis Anonymous, Santa Monica, California",
                image: "img/testimonials/5cdd84c5e09fa5786c765bff_doriancheah.png"
            },
            {
                text: "Thanks for all the hard work Martin. Thanks for going the extra mile there at the end to ensure the deliverables were compatible with my requirements!",
                author: "Ian Enders",
                role: "Chief Technology Officer @ NewlyWish, WeddingWire, New York",
                image: "img/testimonials/5cdd85e3dd5626c2f47d80c7_buf8szic4sdcuwyfdssw.jpeg"
            },
            {
                text: "Martin is a very good designer and we loved his work.",
                author: "Dr. Reinhard Vogel",
                role: "Owner of Cultuzz Digital Media GmbH, Germany",
                image: "img/testimonials/5cdd876afcbd74fc3a7c6427_0.jpeg"
            },
            {
                text: "Excellent communication. Professional and quick turnaround.",
                author: "Gregg F.",
                role: "Founder of ExoticSpotter",
                image: ""
            },
            {
                text: "As always, great work, and a great experience working with Martin. His professional integrity and talents are much appreciated. Great to find somebody who really cares. Martin did excellent work, and his commitment to designing a quality product and a great user experience was extremely valuable to us. Highly recommend!",
                author: "Dorian Cheah",
                role: "Co-founder of Yogis Anonymous, Santa Monica, California",
                image: "img/testimonials/5cdd84c5e09fa5786c765bff_doriancheah.png"
            },
            {
                text: "Martin is a talented webmaster, who strives to find a solution for each problem and is not afraid to explore new ways to achieve it. His knowledge of javascript and algorithmic thinking goes far beyond the level of an average HTML coder. Martin would be aperfect fit for building a new web project from the ground up.",
                author: "Ondřej Brablc",
                role: "Director of Operations at Shoptet",
                image: "img/testimonials/6019030c4e95eb934af55c05_1585763238755.jpeg"
            }
        ];
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const loadingOverlay = document.querySelector('.loading-overlay');
            const loadingBarRight = document.querySelector('.loading-bar');
            const loadingBarLeft = document.querySelector('.loading-bar-left');

            // Start the loading bar animation
            setTimeout(() => {
                loadingBarRight.style.transition = 'transform 0.6s ease-in-out';
                loadingBarLeft.style.transition = 'transform 0.6s ease-in-out';
                loadingBarRight.style.transform = 'scaleX(1)';
                loadingBarLeft.style.transform = 'scaleX(1)';
            }, 100);

            // Hide the overlay after the loading bar completes
            setTimeout(() => {
                loadingOverlay.classList.add('hide-loader');
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 400);
            }, 300);
        });
    </script>
    <script>
        // Check for thanks parameter in URL
        function checkThankYou() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('thanks')) {
                const thankYouMessage = document.getElementById('thankYouMessage');
                thankYouMessage.classList.remove('hidden');
                // Remove the thanks parameter from URL without refreshing
                window.history.replaceState({}, document.title, window.location.pathname);
            }
        }

        // Close thank you message
        function closeThankYou() {
            const thankYouMessage = document.getElementById('thankYouMessage');
            thankYouMessage.classList.add('opacity-0');
            setTimeout(() => {
                thankYouMessage.classList.add('hidden');
                thankYouMessage.classList.remove('opacity-0');
            }, 300);
        }

        // Run check when page loads
        document.addEventListener('DOMContentLoaded', checkThankYou);

        // Add this to your CSS
        document.head.insertAdjacentHTML('beforeend', `
            <style>
                #thankYouMessage {
                    transition: opacity 0.3s ease-in-out;
                }
                
                @keyframes checkmark {
                    0% { transform: scale(0); }
                    50% { transform: scale(1.2); }
                    100% { transform: scale(1); }
                }
                
                #thankYouMessage svg {
                    animation: checkmark 0.5s ease-in-out forwards;
                }
            </style>
        `);
    </script>
    <script>
        // Mobile Menu Functionality
        const mobileMenuButton = document.getElementById('mobileMenuButton');
        const mobileMenuPanel = document.getElementById('mobileMenuPanel');

        if (mobileMenuButton && mobileMenuPanel) {
            mobileMenuButton.addEventListener('click', () => {
                console.log('Menu button clicked'); // Debug line
                mobileMenuPanel.classList.toggle('hidden');
            });

            // Close menu when clicking links
            const mobileLinks = mobileMenuPanel.querySelectorAll('a');
            mobileLinks.forEach(link => {
                link.addEventListener('click', () => {
                    mobileMenuPanel.classList.add('hidden');
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!mobileMenuButton.contains(e.target) &&
                    !mobileMenuPanel.contains(e.target) &&
                    !mobileMenuPanel.classList.contains('hidden')) {
                    mobileMenuPanel.classList.add('hidden');
                }
            });
        }
    </script>
    <script>
        const swiper = new Swiper('.testimonialSwiper', {
            slidesPerView: 'auto',
            centeredSlides: true,
            spaceBetween: 30,
            grabCursor: true,
            loop: true,

            breakpoints: {
                320: {
                    slidesPerView: 1.15,
                    spaceBetween: 20,
                    centeredSlides: true,
                },
                768: {
                    slidesPerView: 1,
                    spaceBetween: 0,
                    centeredSlides: false,
                }
            },

            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            }
        });
        // Add navigation using the old working method
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        prevBtn.addEventListener('click', () => swiper.slidePrev());
        nextBtn.addEventListener('click', () => swiper.slideNext());

        // Populate slides
        const swiperWrapper = document.querySelector('.swiper-wrapper');
        testimonials.forEach(testimonial => {
            const slide = document.createElement('div');
            slide.className = 'swiper-slide';
            slide.innerHTML = `
                <div class="testimonial-slide pb-8 md:pr-4 md:pl-4">
                    <div class="bg-white p-6 md:p-12 rounded-2xl shadow-lg">
                        <blockquote class="text-lg md:text-2xl text-gray-600 mb-8 md:mb-12">
                            "${testimonial.text}"
                        </blockquote>
                        <div class="flex items-center gap-4 md:gap-6">
                            <img data-src="${testimonial.image}" alt="${testimonial.author}" class="w-12 h-12 md:w-16 md:h-16 rounded-full object-cover progressive-image">
                            <div>
                                <h3 class="text-lg md:text-xl font-extrabold text-gray-900">${testimonial.author}</h3>
                                <p class="text-base md:text-lg text-gray-600">${testimonial.role}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            swiperWrapper.appendChild(slide);
        });

        // Initialize optimized images for testimonials after they're created
        setTimeout(() => {
            const testimonialImages = document.querySelectorAll('.swiper-slide img[data-src]');
            testimonialImages.forEach(img => {
                if (window.optimizeImage) {
                    window.optimizeImage(img, false);
                }
            });
        }, 100);
    </script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
    <script type="text/javascript">
        (function () {
            emailjs.init({
                publicKey: "td0bdjr9qK9pixfYQ",
            });
        })();

        window.onload = function() {
        document.getElementById('contactForm').addEventListener('submit', function(event) {
            event.preventDefault();
            
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = 'Sending...';
            submitButton.disabled = true;
            emailjs.sendForm('service_998cfth', 'template_tdkv47e', this)
                .then(() => {
                    console.log('SUCCESS!');
                    this.reset();
                    window.location.href = '/?thanks';
                })
                .catch((error) => {
                    console.log('FAILED...', error);
                    alert('Sorry, there was an error sending your message. Please try again.');
                })
                .finally(() => {
                    submitButton.innerHTML = originalButtonText;
                    submitButton.disabled = false;
                });
        });
    }
    </script>
</body>

</html>