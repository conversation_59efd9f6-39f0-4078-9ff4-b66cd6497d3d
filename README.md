# <PERSON> - Personal Website

A high-performance personal website with advanced image optimization and Cloudflare Workers deployment.

## 🚀 Performance Features

### ✅ **Automatic Image Optimization**
- WebP conversion with 60-94% size reduction
- Tiny placeholder generation (~500B each)
- Smart caching (only processes changed images)
- Progressive loading with blur-to-sharp transitions
- Intersection Observer lazy loading
- WebP detection with automatic fallbacks

### ✅ **Font Loading Optimization**
- Eliminates FOUT (Flash of Unstyled Text)
- System font fallbacks for instant display
- Critical font preloading
- Font loading detection

### ✅ **Critical Resource Preloading**
- Hero images preloaded for instant display
- Above-the-fold content prioritized
- Smart resource hints

## 📁 Project Structure

```
martindoubravsky.com/
├── scripts/
│   └── optimize-images.sh          # Image optimization script
├── js/
│   └── optimized-images.js         # Progressive image loading
├── img/                            # Images directory
│   ├── *.webp                      # WebP optimized versions
│   ├── *-optimized.*               # Optimized originals
│   └── *-low.jpg                   # Tiny placeholders
├── workers-site/
│   └── index.js                    # Cloudflare Workers handler
├── index.html                      # Main website
├── package.json                    # Dependencies and scripts
├── wrangler.toml                   # Cloudflare configuration
└── _headers                        # Cache headers
```

## 🛠️ Development

### Prerequisites
- Node.js 18+
- ImageMagick (`brew install imagemagick` on macOS)

### Setup
```bash
# Install dependencies
npm install

# Optimize images
npm run optimize-images

# Start development server
npm run preview
```

### Available Scripts
```bash
npm run build          # Build with automatic optimization (local)
npm run build:ci       # Build without optimization (CI/CD)
npm run build:fast     # Build without optimization (dev)
npm run optimize-images # Optimize all images manually
npm run optimize-image filename.png # Optimize specific image
npm run deploy         # Build + optimize + deploy to Cloudflare
npm run cf:dev         # Cloudflare Workers development
npm run preview        # Local preview server
```

## 🌐 Cloudflare Workers Deployment

### First-time Setup
1. **Install Wrangler CLI**:
   ```bash
   npm install -g wrangler
   ```

2. **Login to Cloudflare**:
   ```bash
   npx wrangler login
   ```

3. **Update wrangler.toml** with your domain:
   ```toml
   routes = [
     { pattern = "yourdomain.com/*", zone_name = "yourdomain.com" },
     { pattern = "www.yourdomain.com/*", zone_name = "yourdomain.com" }
   ]
   ```

4. **Deploy**:
   ```bash
   npm run deploy
   ```

   **Note**: If you get an error about `wrangler versions upload`, make sure your Cloudflare dashboard is set to use `npx wrangler deploy --legacy-env false` as the deploy command, not the newer versioning system.

### Cloudflare Workers Dashboard Settings
- **Build command**: `npm run build:ci`
- **Deploy command**: `npx wrangler deploy --legacy-env false`
- **Build output directory**: `./`
- **Root directory**: `/` (leave empty or set to root)

**Important**: Make sure to use `wrangler deploy` (not `wrangler versions upload`) as Workers Sites doesn't support the newer versioning system.

### Why These Settings?
- **`build:ci`**: Skips image optimization in CI (no ImageMagick dependency)
- **Pre-optimized images**: Uses optimized images from local development
- **Reliable deployment**: No build failures due to missing dependencies

## 📊 Performance Results

### **Image Optimization:**
- **60-94% file size reduction** with WebP
- **Instant loading** with tiny placeholders (~500B)
- **Smooth transitions** with progressive loading
- **Automatic fallbacks** for older browsers

### **Font Loading:**
- **No FOUT** (Flash of Unstyled Text)
- **Instant text display** with system fonts
- **Smooth transitions** to web fonts

### **Overall Performance:**
- **75-80% bandwidth reduction**
- **Professional loading experience**
- **Excellent Core Web Vitals scores**
- **Fast on all devices and connections**

## 🔧 Technical Features

### **Smart Optimization:**
- Only processes changed images
- Maintains file timestamps
- Skips already optimized files
- Automatic during build process

### **Progressive Enhancement:**
- Works without JavaScript
- Graceful fallbacks everywhere
- WebP detection with PNG/JPG fallback
- System fonts while web fonts load

### **Performance Monitoring:**
- Intersection Observer for lazy loading
- Optimized cache headers
- Preloading for critical resources

## 🚀 Deployment Workflow

1. **Develop locally**: Images get optimized automatically during `npm run build`
2. **Commit optimized images**: All `.webp`, `-optimized.*`, and `-low.jpg` files
3. **Deploy**: Cloudflare uses pre-optimized images, skips optimization step
4. **Result**: Fast deployment with optimized images

## 💡 Maintenance

### **Adding New Images:**
1. Add image to `img/` directory
2. Run `npm run optimize-image filename.ext`
3. Use optimized versions in HTML with `data-src` attribute
4. Build automatically optimizes all images

### **Performance Monitoring:**
- Use browser DevTools to verify optimizations
- Check WebP support and fallbacks
- Monitor Core Web Vitals
- Test on slow connections

## 🎯 What You Get

✅ **Fast image loading** (60-94% smaller files)  
✅ **Instant text display** without font flash  
✅ **Smooth, professional loading experience**  
✅ **Global CDN** distribution via Cloudflare  
✅ **Automatic HTTPS** and security headers  
✅ **Excellent performance** on all devices  

## 📞 Contact

Martin Doubravský  
Email: <EMAIL>  
Website: https://martindoubravsky.com
