// Optimized Image Loading for Static HTML Site
// Provides progressive loading with WebP support and lazy loading

class OptimizedImageLoader {
    constructor() {
        this.supportsWebP = null;
        this.init();
    }

    async init() {
        this.supportsWebP = await this.checkWebPSupport();
        this.setupLazyLoading();
        this.preloadCriticalImages();
    }

    // Check if browser supports WebP
    checkWebPSupport() {
        return new Promise((resolve) => {
            const webP = new Image();
            webP.onload = webP.onerror = () => {
                resolve(webP.height === 2);
            };
            webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        });
    }

    // Get optimized image sources
    getOptimizedSources(originalSrc) {
        const pathParts = originalSrc.split('.');
        const extension = pathParts.pop();
        const basePath = pathParts.join('.');

        return {
            webp: `${basePath}.webp`,
            optimized: `${basePath}-optimized.${extension}`,
            placeholder: `${basePath}-low.jpg`,
            original: originalSrc
        };
    }

    // Create progressive image element
    createProgressiveImage(img, priority = false) {
        const originalSrc = img.dataset.src || img.src;

        // Skip if no source provided
        if (!originalSrc || originalSrc.trim() === '') {
            return;
        }

        const sources = this.getOptimizedSources(originalSrc);
        
        // Set placeholder immediately
        img.src = sources.placeholder;
        img.style.filter = 'blur(5px)';
        img.style.transform = 'scale(1.05)';
        img.style.transition = 'all 0.5s ease-out';

        // Load high-quality image
        const loadHighQuality = () => {
            const highQualityImg = new Image();
            const targetSrc = this.supportsWebP ? sources.webp : sources.optimized;

            highQualityImg.onload = () => {
                img.src = targetSrc;
                img.style.filter = 'none';
                img.style.transform = 'scale(1)';
                img.classList.add('loaded');
            };

            highQualityImg.onerror = () => {
                // Fallback to original if optimized version fails
                const fallbackImg = new Image();
                fallbackImg.onload = () => {
                    img.src = sources.original;
                    img.style.filter = 'none';
                    img.style.transform = 'scale(1)';
                    img.classList.add('loaded');
                };
                fallbackImg.src = sources.original;
            };

            highQualityImg.src = targetSrc;
        };

        if (priority) {
            // Load immediately for critical images
            loadHighQuality();
        } else {
            // Use Intersection Observer for lazy loading
            this.observeImage(img, loadHighQuality);
        }
    }

    // Set up Intersection Observer for lazy loading
    observeImage(img, loadCallback) {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        loadCallback();
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            observer.observe(img);
        } else {
            // Fallback for browsers without Intersection Observer
            loadCallback();
        }
    }

    // Setup lazy loading for all images with data-src
    setupLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            const priority = img.dataset.priority === 'true';
            this.createProgressiveImage(img, priority);
        });
    }

    // Preload critical images
    preloadCriticalImages() {
        const criticalImages = [
            'img/jvzXHNbTB6wGm1VyXrtpo0NF0SL2D0M4o8pBKGCF.png', // Hero image
            'img/5cf549cc73a6231e45b94e50_IMG_5923.jpg' // About image
        ];

        criticalImages.forEach(src => {
            const sources = this.getOptimizedSources(src);
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = this.supportsWebP ? sources.webp : sources.optimized;
            document.head.appendChild(link);
        });
    }

    // Progressive background image loading
    loadProgressiveBackground(element, imageSrc, priority = false) {
        const sources = this.getOptimizedSources(imageSrc);
        
        // Set placeholder background
        element.style.backgroundImage = `url(${sources.placeholder})`;
        element.style.filter = 'blur(2px)';
        element.style.transform = 'scale(1.02)';
        element.style.transition = 'all 0.5s ease-out';

        const loadHighQuality = () => {
            const img = new Image();
            const targetSrc = this.supportsWebP ? sources.webp : sources.optimized;

            img.onload = () => {
                element.style.backgroundImage = `url(${targetSrc})`;
                element.style.filter = 'none';
                element.style.transform = 'scale(1)';
                element.classList.add('bg-loaded');
            };

            img.onerror = () => {
                // Fallback to original
                const fallbackImg = new Image();
                fallbackImg.onload = () => {
                    element.style.backgroundImage = `url(${sources.original})`;
                    element.style.filter = 'none';
                    element.style.transform = 'scale(1)';
                    element.classList.add('bg-loaded');
                };
                fallbackImg.src = sources.original;
            };

            img.src = targetSrc;
        };

        if (priority) {
            loadHighQuality();
        } else {
            this.observeImage(element, loadHighQuality);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.optimizedImageLoader = new OptimizedImageLoader();
});

// Utility function for manual image optimization
window.optimizeImage = (img, priority = false) => {
    if (window.optimizedImageLoader) {
        window.optimizedImageLoader.createProgressiveImage(img, priority);
    }
};

// Utility function for background images
window.optimizeBackgroundImage = (element, imageSrc, priority = false) => {
    if (window.optimizedImageLoader) {
        window.optimizedImageLoader.loadProgressiveBackground(element, imageSrc, priority);
    }
};
