name = "martindoubravsky-com"
main = "workers-site/index.js"
compatibility_date = "2024-01-01"

[site]
bucket = "./"

# Custom domain configuration (update with your actual domain)
# routes = [
#   { pattern = "martindoubravsky.com/*", zone_name = "martindoubravsky.com" },
#   { pattern = "www.martindoubravsky.com/*", zone_name = "martindoubravsky.com" }
# ]

# Environment variables (if needed)
# [vars]
# ENVIRONMENT = "production"
