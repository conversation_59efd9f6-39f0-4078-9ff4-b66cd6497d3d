# Cloudflare Pages Configuration
# For Workers Sites deployment (alternative approach)
name = "martindoubravsky-com"
main = "workers-site/index.js"
compatibility_date = "2024-01-01"

[site]
bucket = "./"

# Custom domain configuration (update with your actual domain)
# routes = [
#   { pattern = "martindoubravsky.com/*", zone_name = "martindoubravsky.com" },
#   { pattern = "www.martindoubravsky.com/*", zone_name = "martindoubravsky.com" }
# ]

# Environment variables (if needed)
# [vars]
# ENVIRONMENT = "production"

# Note: For easier deployment, consider using Cloudflare Pages instead
# Pages settings:
# - Framework preset: None
# - Build command: npm run build:ci
# - Build output directory: ./
# - Root directory: /
