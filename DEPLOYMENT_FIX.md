# Cloudflare Workers Deployment Fix

## Issue
The deployment is failing with this error:
```
✘ [ERROR] Workers Sites does not support uploading versions through `wrangler versions upload`. You must use `wrangler deploy` instead.
```

## Solution

### 1. Update Cloudflare Dashboard Settings

In your Cloudflare dashboard, make sure these settings are configured:

**Build Settings:**
- **Build command**: `npm run build:ci`
- **Deploy command**: `npx wrangler deploy --legacy-env false`
- **Build output directory**: `./`
- **Root directory**: `/` (or leave empty)

### 2. Alternative: Manual Deployment

If the dashboard deployment continues to fail, you can deploy manually:

```bash
# Login to Cloudflare (if not already logged in)
npx wrangler login

# Deploy manually
npm run deploy
```

### 3. Verify wrangler.toml Configuration

Make sure your `wrangler.toml` is correctly configured:

```toml
name = "martindoubravsky-com"
main = "workers-site/index.js"
compatibility_date = "2024-01-01"

[site]
bucket = "./"
```

### 4. Check Wrangler Version

Ensure you're using a compatible version of Wrangler:

```bash
npx wrangler --version
```

Should be version 4.x or higher.

### 5. Troubleshooting

If you continue to have issues:

1. **Clear Wrangler cache**:
   ```bash
   npx wrangler logout
   npx wrangler login
   ```

2. **Try deploying without the dashboard**:
   ```bash
   npx wrangler deploy --legacy-env false
   ```

3. **Check your Cloudflare account permissions** - make sure you have Workers deployment permissions.

## Why This Happens

Cloudflare introduced a new versioning system for Workers, but Workers Sites (which we're using for static file serving) doesn't support this new system yet. It still requires the classic `wrangler deploy` command.

The key is ensuring that Cloudflare uses `wrangler deploy` instead of `wrangler versions upload` for deployment.
