.w-layout-grid {
  display: -ms-grid;
  display: grid;
  grid-auto-columns: 1fr;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
  grid-row-gap: 16px;
  grid-column-gap: 16px;
}

body {
  font-family: Montserrat, sans-serif;
  color: #1a1b1f;
  font-size: 16px;
  line-height: 28px;
  font-weight: 400;
}

h1 {
  margin-top: 20px;
  margin-bottom: 15px;
  font-size: 44px;
  line-height: 62px;
  font-weight: 400;
}

h2 {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 36px;
  line-height: 50px;
  font-weight: 400;
}

h3 {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 30px;
  line-height: 46px;
  font-weight: 400;
}

h4 {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 24px;
  line-height: 38px;
  font-weight: 400;
}

h5 {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 20px;
  line-height: 34px;
  font-weight: 500;
}

h6 {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 16px;
  line-height: 28px;
  font-weight: 500;
}

p {
  margin-bottom: 10px;
  font-family: niveau-grotesk, sans-serif;
  font-size: 20px;
  line-height: 1.6;
}

a {
  display: block;
  -webkit-transition: opacity 200ms ease;
  transition: opacity 200ms ease;
  color: #1a1b1f;
  text-decoration: underline;
}

a:hover {
  color: #32343a;
}

a:active {
  color: #43464d;
}

ul {
  margin-top: 20px;
  margin-bottom: 20px;
  padding-left: 40px;
  list-style-type: disc;
}

li {
  margin-bottom: 10px;
}

img {
  display: block;
}

label {
  margin-bottom: 10px;
  font-size: 12px;
  line-height: 20px;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
}

strong {
  font-weight: bold;
}

blockquote {
  margin-top: 25px;
  margin-bottom: 25px;
  padding: 15px 30px;
  border-left: 5px solid #e2e2e2;
  font-size: 20px;
  line-height: 34px;
}

figure {
  margin-top: 25px;
  padding-bottom: 20px;
}

figcaption {
  margin-top: 5px;
  opacity: 0.6;
  font-size: 14px;
  line-height: 26px;
  text-align: center;
}

.heading-jumbo-small {
  margin-top: 10px;
  margin-bottom: 15px;
  font-size: 36px;
  line-height: 50px;
  font-weight: 400;
  text-transform: none;
}

.styleguide-block {
  display: block;
  margin-top: 80px;
  margin-bottom: 80px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
}

.heading-jumbo-tiny {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 18px;
  line-height: 32px;
  font-weight: 500;
  text-transform: uppercase;
}

.rich-text {
  width: 70%;
  margin-right: auto;
  margin-bottom: 100px;
  margin-left: auto;
}

.rich-text p {
  margin-top: 15px;
  margin-bottom: 25px;
  opacity: 0.6;
}

.container {
  display: block;
  width: 100%;
  max-width: 1300px;
  margin: 0px auto;
  padding: 0px 40px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  grid-auto-columns: 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  -ms-grid-columns: auto 1fr;
  grid-template-columns: auto 1fr;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
}

.container.container-header {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-width: 1400px;
  margin-top: 40px;
  margin-bottom: 0px;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: auto;
  -ms-flex-item-align: auto;
  align-self: auto;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.container.container-contact {
  margin-top: 0px;
  margin-bottom: 0px;
}

.container.grid {
  display: -ms-grid;
  display: grid;
  max-width: 1500px;
  padding-top: 100px;
  padding-bottom: 140px;
  justify-items: start;
  -webkit-box-align: start;
  -webkit-align-items: start;
  -ms-flex-align: start;
  align-items: start;
  grid-auto-flow: row dense;
  grid-auto-columns: 1fr;
  grid-column-gap: 55px;
  grid-row-gap: 21px;
  grid-template-areas: ".";
  -ms-grid-columns: minmax(auto, 1fr) 55px 1fr;
  grid-template-columns: minmax(auto, 1fr) 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
  background-color: transparent;
}

.styleguide-content-wrap {
  text-align: center;
}

.paragraph-small {
  font-size: 14px;
  line-height: 26px;
}

.styleguide-header-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 460px;
  padding: 30px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #1a1b1f;
  color: #fff;
  text-align: center;
}

.styleguide-button-wrap {
  margin-top: 10px;
  margin-bottom: 10px;
}

.heading-jumbo {
  position: static;
  left: -500px;
  display: inline-block;
  margin-top: 10px;
  margin-bottom: 1em;
  font-family: poynter-oldstyle-display;
  color: #ffed61;
  font-size: 55px;
  line-height: 89px;
  font-weight: 700;
  text-transform: none;
  text-shadow: 1px 1px 1px #000;
}

.heading-jumbo.heading-jumbo-2 {
  margin-bottom: 0em;
  color: #fff;
}

.heading-jumbo.heading-gradient {
  display: block;
  margin-top: 0px;
  margin-bottom: 0em;
  padding-bottom: 8px;
  direction: ltr;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #180a22;
  font-size: 60px;
  line-height: 1.2;
  font-weight: 700;
  text-align: left;
  letter-spacing: -0.06em;
  text-transform: none;
  text-shadow: none;
}

.heading-jumbo.heading-gradient.h1-l {
  width: 100%;
  padding-bottom: 40px;
  font-size: 84px;
  text-align: left;
}

.heading-jumbo.heading-gradient-2 {
  margin-bottom: 10px;
  color: #1a1b1f;
  text-transform: none;
  text-shadow: none;
}

.heading-jumbo.heading-error {
  font-family: Montserrat, sans-serif;
  font-size: 48px;
  line-height: 57px;
}

.paragraph-tiny {
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  font-size: 15px;
  line-height: 18px;
  font-weight: 700;
  text-transform: none;
}

.paragraph-tiny.cc-paragraph-tiny-light {
  opacity: 1;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #fff;
  font-size: 20px;
  line-height: 30px;
  font-weight: 700;
  letter-spacing: 0.02em;
  text-transform: none;
}

.paragraph-tiny.footer {
  color: #4c3714;
  font-weight: 500;
}

.label {
  margin-bottom: 10px;
  font-size: 12px;
  line-height: 20px;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.label.cc-styleguide-label {
  margin-bottom: 25px;
}

.label.cc-speaking-label {
  margin-top: 20px;
  margin-bottom: 10px;
}

.label.cc-about-light {
  opacity: 0.6;
}

.paragraph-light {
  opacity: 0.6;
  font-size: 21px;
  line-height: 34px;
  font-weight: 400;
}

.paragraph-light.cc-position-name {
  margin-bottom: 5px;
}

.section {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 100vh;
  margin-right: 0px;
  margin-left: 0px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  grid-auto-columns: 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
  background-image: url('../images/IMG_5587-edit.jpg');
  background-position: 100% 50%;
  background-size: cover;
}

.section.cc-contact {
  display: block;
  height: auto;
  min-height: auto;
  padding: 140px 40px;
  background-color: #62b1ff;
  background-image: none;
  font-size: 16px;
}

.section.hero {
  height: auto;
  min-height: 95vh;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background-image: linear-gradient(126deg, #9039cc, #f1189b);
}

.section.hero.sub {
  display: block;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  background-image: linear-gradient(126deg, #9039cc, #f1189b);
  background-position: 0px 0px;
  background-size: auto;
  background-repeat: repeat;
}

.section.section-bg {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: auto;
  padding-top: 140px;
  padding-bottom: 140px;
  background-image: linear-gradient(150deg, #fff, #fffa6e 52%, #97000a);
}

.button {
  margin-right: 1em;
  padding: 12px 25px;
  border-radius: 4px;
  background-color: #a0f2e2;
  -webkit-transition: background-color 400ms ease, opacity 400ms ease, color 400ms ease;
  transition: background-color 400ms ease, opacity 400ms ease, color 400ms ease;
  color: #000;
  font-size: 12px;
  line-height: 20px;
  font-weight: 500;
  letter-spacing: 2px;
  text-decoration: none;
  text-transform: uppercase;
}

.button:hover {
  background-color: #62b1ff;
  color: #fff;
}

.button:active {
  background-color: #43464d;
}

.button.cc-jumbo-button {
  padding: 16px 35px;
  font-size: 14px;
  line-height: 26px;
}

.button.cc-white-button {
  padding: 16px 35px;
  background-color: #ffed61;
  color: #202020;
  font-size: 14px;
  line-height: 26px;
}

.button.cc-white-button:hover {
  background-color: hsla(0, 0%, 100%, 0.8);
}

.button.cc-white-button:active {
  background-color: hsla(0, 0%, 100%, 0.9);
}

.button.button-form {
  box-shadow: 1px 1px 1px 0 rgba(0, 0, 0, 0.2);
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 1px;
}

.paragraph-bigger {
  margin-bottom: 10px;
  opacity: 1;
  font-size: 20px;
  line-height: 34px;
  font-weight: 400;
}

.paragraph-bigger.cc-bigger-light {
  opacity: 1;
  font-family: Montserrat, sans-serif;
  color: #fff;
  font-size: 21px;
  font-weight: 400;
}

.divider {
  height: 1px;
  background-color: #eee;
}

.logo-link {
  z-index: 1;
}

.logo-link:hover {
  opacity: 0.8;
}

.logo-link:active {
  opacity: 0.7;
}

.navigation-item {
  padding-top: 9px;
  padding-bottom: 9px;
  border: 1px solid #000;
  border-radius: 3px;
  opacity: 0.6;
  font-size: 12px;
  line-height: 20px;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.navigation-item:hover {
  opacity: 0.9;
}

.navigation-item:active {
  opacity: 0.8;
}

.navigation-item.w--current {
  opacity: 1;
  color: #1a1b1f;
  font-weight: 600;
}

.navigation-item.w--current:hover {
  opacity: 0.8;
  color: #32343a;
}

.navigation-item.w--current:active {
  opacity: 0.7;
  color: #32343a;
}

.navigation-items {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.navigation {
  z-index: 1;
  display: none;
  width: 100%;
  max-width: 1400px;
  margin-right: auto;
  margin-left: auto;
  padding: 20px 30px 20px 10px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-ordinal-group: 1;
  -webkit-order: 0;
  -ms-flex-order: 0;
  order: 0;
  background-color: transparent;
}

.logo-image {
  display: block;
}

.navigation-wrap {
  display: none;
  margin-right: 0px;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.intro-wrap {
  display: block;
  margin-top: 0px;
  margin-bottom: 0px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
}

.name-text {
  font-size: 20px;
  line-height: 34px;
  font-weight: 400;
}

.position-name-text {
  margin-bottom: 10px;
  font-size: 20px;
  line-height: 34px;
  font-weight: 400;
  text-transform: none;
}

.work-description {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  margin-bottom: 60px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.work-experience-grid {
  margin-bottom: 140px;
  grid-column-gap: 30px;
  grid-row-gap: 30px;
  grid-template-areas: ". . . .";
  -ms-grid-columns: 1fr 30px 1fr 30px 1fr 30px 1fr;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.works-grid {
  margin-bottom: 80px;
  grid-column-gap: 30px;
  grid-row-gap: 30px;
  grid-template-areas: ". . ."
    ". . .";
  -ms-grid-columns: 1.5fr 30px 1fr 30px 1.5fr;
  grid-template-columns: 1.5fr 1fr 1.5fr;
  -ms-grid-rows: auto 30px auto;
  grid-template-rows: auto auto;
}

.carrer-headline-wrap {
  width: 70%;
  margin-bottom: 50px;
}

.work-image {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 460px;
  margin-bottom: 40px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background-color: #f4f4f4;
  background-image: url('https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg');
  background-position: 50% 50%;
  background-size: cover;
  text-align: center;
  text-decoration: none;
}

.work-image:hover {
  opacity: 0.8;
}

.work-image:active {
  opacity: 0.7;
}

.work-image.cc-work-1 {
  background-image: url('../images/portfolio-1---wide.svg');
  background-size: cover;
}

.work-image.cc-work-2 {
  background-image: url('../images/portfolio-2---wide.svg');
  background-size: cover;
}

.work-image.cc-work-4 {
  background-image: url('../images/portfolio-3---wide.svg');
  background-size: cover;
}

.work-image.cc-work-3 {
  background-image: url('../images/portfolio-4---wide.svg');
  background-size: cover;
}

.project-name-link {
  margin-bottom: 5px;
  font-size: 20px;
  line-height: 34px;
  font-weight: 400;
  text-decoration: none;
}

.project-name-link:hover {
  opacity: 0.8;
}

.project-name-link:active {
  opacity: 0.7;
}

.text-field {
  margin-bottom: 0px;
  padding: 40px;
  border: 0px none #e4e4e4;
  border-radius: 0px;
  box-shadow: 1px 1px 1px 0 rgba(0, 0, 0, 0.2);
  -webkit-transition: border-color 400ms ease;
  transition: border-color 400ms ease;
  font-family: niveau-grotesk, sans-serif;
  font-size: 24px;
  font-weight: 400;
}

.text-field:hover {
  border-color: #e3e6eb;
}

.text-field:active {
  border-color: #43464d;
}

.text-field:focus {
  border-color: #43464d;
}

.text-field::-webkit-input-placeholder {
  color: rgba(24, 10, 34, 0.79);
  font-weight: 300;
}

.text-field:-ms-input-placeholder {
  color: rgba(24, 10, 34, 0.79);
  font-weight: 300;
}

.text-field::-ms-input-placeholder {
  color: rgba(24, 10, 34, 0.79);
  font-weight: 300;
}

.text-field::placeholder {
  color: rgba(24, 10, 34, 0.79);
  font-weight: 300;
}

.text-field.cc-textarea {
  height: 200px;
  margin-bottom: 0px;
  padding-top: 30px;
}

.status-message {
  padding: 9px 30px;
  background-color: #202020;
  color: #fff;
  font-size: 14px;
  line-height: 26px;
  text-align: center;
}

.status-message.cc-success-message {
  position: fixed;
  left: 0%;
  top: 0%;
  right: 0%;
  bottom: 0%;
  z-index: 5;
  padding: 0px;
  background-color: transparent;
}

.status-message.cc-success-message.perex {
  padding: 0px;
}

.status-message.cc-success-message.perex.perex-highlight {
  padding-right: 0px;
  padding-left: 0px;
  text-align: left;
}

.status-message.cc-error-message {
  position: fixed;
  left: 0%;
  top: 0%;
  right: 0%;
  bottom: 0%;
  z-index: 5;
  margin-top: 0px;
  padding: 0em;
  border-radius: 4px;
  background-color: transparent;
  text-align: left;
}

.contact {
  padding-top: 0px;
  padding-bottom: 0px;
}

.contact-headline {
  width: 70%;
  margin-bottom: 40px;
}

.contact-form-grid {
  grid-column-gap: 15px;
  grid-row-gap: 15px;
  grid-template-areas: ". ."
    ". ."
    ". ."
    ". Area";
  -ms-grid-columns: 1fr 15px 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto 15px auto 15px auto 15px auto;
  grid-template-rows: auto auto auto auto;
}

.contact-form-wrap {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
}

.footer-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 40px 50px;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #ffc461;
}

.webflow-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 0.5;
  -webkit-transition: opacity 400ms ease;
  transition: opacity 400ms ease;
  text-decoration: none;
  text-transform: uppercase;
}

.webflow-link:hover {
  opacity: 1;
}

.webflow-link:active {
  opacity: 0.8;
}

.webflow-link.w--current {
  margin-right: 30px;
  opacity: 0.8;
}

.webflow-logo-tiny {
  margin-top: -2px;
  margin-right: 8px;
}

.footer-links {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-right: 0px;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.footer-item {
  margin-right: 31px;
  margin-left: 0px;
  opacity: 0.6;
  font-size: 12px;
  line-height: 18px;
  font-weight: 500;
  letter-spacing: 1px;
  text-decoration: none;
  text-transform: uppercase;
}

.footer-item:hover {
  opacity: 0.9;
}

.footer-item:active {
  opacity: 0.8;
}

.about-intro-grid {
  margin-top: 100px;
  margin-bottom: 140px;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-column-gap: 80px;
  grid-row-gap: 30px;
  grid-template-areas: ". .";
  -ms-grid-columns: 1fr 80px 2fr;
  grid-template-columns: 1fr 2fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.hi-there-heading {
  margin-top: 10px;
  margin-bottom: 20px;
}

.service-name-text {
  margin-bottom: 10px;
  opacity: 0.6;
  font-size: 30px;
  line-height: 46px;
}

.skillset-wrap {
  padding-right: 60px;
}

.reference-link {
  opacity: 0.6;
  font-size: 14px;
  line-height: 26px;
  text-decoration: none;
}

.reference-link:hover {
  opacity: 1;
}

.reference-link:active {
  opacity: 0.9;
}

.featured-item-wrap {
  margin-bottom: 25px;
}

.services-items-grid {
  padding-top: 10px;
  grid-column-gap: 30px;
  grid-row-gap: 30px;
  grid-template-areas: ". .";
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.skills-grid {
  margin-bottom: 140px;
  grid-column-gap: 80px;
  grid-row-gap: 30px;
  grid-template-areas: ". .";
  -ms-grid-columns: 1fr 80px 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.personal-features-grid {
  margin-bottom: 110px;
  grid-column-gap: 80px;
  grid-row-gap: 20px;
  grid-template-areas: ". ."
    ". .";
  -ms-grid-rows: auto 20px auto;
  grid-template-rows: auto auto;
}

.speaking-text {
  display: inline-block;
  margin-right: 8px;
}

.speaking-text.cc-past-speaking {
  opacity: 0.6;
}

.speaking-detail {
  display: inline-block;
  opacity: 0.6;
}

.upcoming-wrap {
  margin-bottom: 40px;
}

.social-media-heading {
  margin-bottom: 60px;
}

.social-media-grid {
  margin-bottom: 30px;
  grid-column-gap: 30px;
  grid-row-gap: 30px;
  -ms-grid-rows: auto 30px auto;
  grid-template-areas: ". . . ."
    ". . . .";
  -ms-grid-columns: 1fr 30px 1fr 30px 1fr 30px 1fr;
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

.project-overview-grid {
  margin-top: 120px;
  margin-bottom: 135px;
  grid-column-gap: 50px;
  grid-row-gap: 100px;
  grid-template-areas: ". . . ."
    ". . . .";
  -ms-grid-columns: 1fr 50px 1fr 50px 1fr 50px 1fr;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  -ms-grid-rows: auto 100px auto;
  grid-template-rows: auto auto;
}

.detail-header-image {
  width: 100%;
}

.project-description-grid {
  margin-top: 120px;
  margin-bottom: 120px;
  grid-column-gap: 30px;
  grid-row-gap: 30px;
  grid-template-areas: ". .";
  -ms-grid-columns: 1fr 30px 2.5fr;
  grid-template-columns: 1fr 2.5fr;
  -ms-grid-rows: auto;
  grid-template-rows: auto;
}

.detail-image {
  width: 100%;
  margin-bottom: 30px;
}

.email-section {
  width: 70%;
  margin: 140px auto 200px;
  text-align: center;
}

.email-link {
  margin-top: 15px;
  margin-bottom: 15px;
  font-size: 64px;
  line-height: 88px;
  font-weight: 400;
  text-decoration: none;
  text-transform: none;
}

.email-link:hover {
  opacity: 0.8;
}

.email-link:active {
  opacity: 0.7;
}

.utility-page-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100vw;
  height: 100vh;
  max-height: 100%;
  max-width: 100%;
  padding: 30px;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-image: url('../images/photo-1421930535025-d2af27c14065-2400x1602-98206.jpeg');
  background-position: 50% 100%;
  background-size: cover;
  color: #fff;
  text-align: center;
}

._404-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: 100%;
  padding: 30px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
}

._404-content-wrap {
  margin-bottom: 60px;
}

.protected-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: 90px;
  padding-bottom: 100px;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
}

.protected-form {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.protected-heading {
  margin-bottom: 30px;
}

.body {
  grid-auto-columns: 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto auto auto;
  grid-template-rows: auto auto auto;
  background-color: #fff;
  font-family: ff-meta-serif-web-pro;
}

.link {
  position: static;
  display: inline-block;
  overflow: visible;
  float: none;
}

.text-block {
  font-family: cronos-pro-subhead;
  color: #62b1ff;
  font-size: 19px;
}

.text-span {
  position: relative;
  color: #62b1ff;
  font-size: 31px;
}

.text-span-2 {
  color: #62b1ff;
}

.image {
  margin-right: 5px;
}

.button-2 {
  background-color: #f1189b;
  -webkit-transition-property: background-color;
  transition-property: background-color;
  font-family: open-sans;
  color: #fff;
  font-size: 13px;
  font-weight: 600;
  text-transform: none;
}

.button-2:hover {
  background-color: #62b1ff;
  color: #000;
}

.button-2.button-desktop {
  padding: 8px 12px;
  border-radius: 4px;
  background-color: #62b1ff;
  -webkit-transition: color 200ms ease, background-color 200ms ease, -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  transition: color 200ms ease, background-color 200ms ease, -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  transition: transform 200ms cubic-bezier(.165, .84, .44, 1), color 200ms ease, background-color 200ms ease;
  transition: transform 200ms cubic-bezier(.165, .84, .44, 1), color 200ms ease, background-color 200ms ease, -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #180a22;
  font-size: 14px;
  line-height: 21px;
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.button-2.button-desktop:hover {
  background-color: #62b1ff;
  -webkit-transform: scale(1.05);
  -ms-transform: scale(1.05);
  transform: scale(1.05);
}

.button-2.button-mobile {
  display: none;
}

.div-block {
  display: none;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.image-2 {
  margin-right: 8px;
  padding: 5px;
  border-radius: 50%;
  background-color: rgba(160, 242, 226, 0.3);
  box-shadow: none;
  -webkit-transition: background-color 200ms ease;
  transition: background-color 200ms ease;
}

.image-2:hover {
  background-color: transparent;
  box-shadow: none;
}

.sub-heading {
  max-width: none;
  margin-top: 0px;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #fff;
  font-size: 24px;
  line-height: 1.6;
  font-weight: 700;
  text-align: left;
  text-shadow: none;
  background-clip: border-box;
}

.sub-heading.hero-sub {
  max-width: 25em;
  font-family: niveau-grotesk, sans-serif;
  color: #321447;
  font-size: 24px;
  line-height: 1.5;
  font-weight: 400;
}

.blockquote-author {
  display: block;
  text-align: center;
}

.image-3 {
  display: inline-block;
  margin-right: 10px;
  border-radius: 100%;
}

.text-block-2 {
  display: inline-block;
  padding-top: 8px;
  padding-bottom: 8px;
  font-family: 'Open Sans', sans-serif;
  font-size: 14px;
  line-height: 21px;
  font-weight: 400;
}

.block-quote {
  margin-bottom: 16px;
  padding: 16px 32px;
  border-left-style: solid;
  font-family: Oswald, sans-serif;
  font-size: 30px;
  line-height: 53px;
  font-weight: 400;
  text-align: left;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.16);
}

.section-3 {
  padding-top: 140px;
  padding-bottom: 140px;
}

.section-3.section-portfolio {
  padding-top: 60px;
}

.section-3.section-bg-w {
  background-color: #fff;
}

.section-3.section-testimonails {
  padding-top: 136px;
  padding-bottom: 136px;
  background-color: #180a22;
}

.heading-2 {
  font-family: open-sans;
  color: #fff;
  font-size: 34px;
  font-weight: 600;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.18);
}

.heading-3 {
  margin-bottom: 0.618em;
  font-family: poynter-oldstyle-display;
  color: #42261b;
  font-size: 89px;
  line-height: 120px;
  font-weight: 700;
  text-align: left;
  text-shadow: 2px 2px 1px #fff;
}

.text-span-3 {
  color: #fff;
}

.paragraph {
  margin-bottom: 1em;
  font-family: Montserrat, sans-serif;
  color: #1a1b1f;
  font-size: 21px;
  line-height: 34px;
  font-weight: 400;
  text-shadow: none;
}

.paragraph.paragraph-bigger {
  display: inline-block;
  margin-bottom: 10px;
  font-weight: 300;
}

.paragraph.paragraph-bigger.paragraph-2 {
  margin-bottom: 1em;
  font-size: 21px;
  line-height: 34px;
  font-weight: 500;
}

.section-p {
  padding-top: 140px;
  padding-bottom: 140px;
  background-color: #fff;
}

.section-p.section-avocado {
  padding: 140px 40px;
  background-color: #fff;
  background-image: none;
}

.section-p.section-avocado.aboutme {
  padding-top: 0px;
}

.section-p.section-avocado.desc {
  padding-top: 100px;
  padding-bottom: 100px;
}

.section-p.section-upwork {
  overflow: hidden;
  padding-top: 0px;
  padding-bottom: 0px;
  background-color: transparent;
  color: #1a1b1f;
}

.section-p.section-how {
  padding-top: 140px;
  padding-bottom: 140px;
  background-color: transparent;
}

.section-p.section-work-together {
  padding-right: 40px;
  padding-left: 40px;
  background-color: #222;
}

.section-p.section-bg {
  background-color: #5298dd;
  background-image: none;
  background-size: auto;
}

.section-p.section-bg.kecyvkleci {
  display: block;
}

.section-p.section-light {
  padding-right: 40px;
  padding-left: 40px;
  background-color: #fff;
}

.section-p.section-temp {
  padding: 192px 40px;
  background-color: #e7e7e7;
}

.section-p.casestudy-section {
  margin-top: 40px;
  padding-top: 0px;
  padding-bottom: 0px;
}

.text-span-4 {
  font-weight: 600;
}

.text-span-5 {
  color: rgba(0, 0, 0, 0.24);
}

.link-2 {
  display: inline-block;
  font-family: Montserrat, sans-serif;
  color: #fff;
  font-weight: 600;
  text-decoration: underline;
  text-shadow: none;
}

.link-2:hover {
  color: #00ffcd;
  text-shadow: none;
}

.link-2.link-upwork {
  color: #fff;
  font-weight: 700;
}

.text-span-6 {
  max-width: 37em;
  margin-bottom: 0px;
  color: #fff;
  font-size: 21px;
  line-height: 34px;
  font-weight: 400;
  text-shadow: none;
}

.text-span-6.text-white {
  color: #fff;
}

.h2 {
  display: block;
  margin-top: 0px;
  margin-bottom: 1em;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #180a22;
  font-size: 60px;
  line-height: 1.3;
  font-weight: 700;
  text-align: left;
  letter-spacing: 0px;
  text-shadow: 2px 2px 0 rgba(26, 27, 31, 0.06);
}

.h2.h1-c {
  line-height: 110px;
  text-align: center;
}

.h2.h1-smaller {
  color: #1a1b1f;
  font-size: 96px;
  line-height: 106px;
}

.h2.h1-good {
  margin-bottom: 48px;
  font-size: 96px;
  line-height: 106px;
}

.h2.h1-p {
  margin-bottom: 0.25em;
}

.h2.h1-w {
  width: 100%;
  margin-bottom: 0em;
  padding-top: 0.5em;
  padding-bottom: 0.25em;
  font-size: 5vw;
  line-height: 150%;
  text-align: center;
}

.h2.h3.h3-c.h3-light {
  margin-top: 32px;
  font-family: Montserrat, sans-serif;
  color: #fff;
  font-size: 30px;
  font-weight: 700;
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.21);
}

.h2.h2-alt {
  display: block;
  margin-bottom: 40px;
  color: rgba(0, 0, 0, 0.8);
  text-shadow: 2px 2px 1px rgba(26, 27, 31, 0.16);
}

.h2.h2-light {
  margin-bottom: 1em;
  color: #fff;
  text-align: center;
}

.h2.h2--whatdo {
  font-size: 20px;
  line-height: 1.5;
  font-weight: 300;
}

.h2.h2--whatdo.bold {
  font-weight: 700;
}

.bold-text {
  color: #fff;
  font-weight: 600;
  text-shadow: 1px 1px 0 rgba(26, 27, 31, 0.39);
}

.bold-text.bold-shadow-inverted {
  font-size: 34px;
  font-weight: 800;
  text-shadow: 1px 1px 0 rgba(26, 27, 31, 0.15);
}

.bold-text.bold-text-2 {
  font-family: poynter-oldstyle-display;
  line-height: 55px;
}

.bold-text-2 {
  font-size: 34px;
  font-weight: 600;
  text-shadow: none;
}

.bold-text-2.bold-shadow {
  font-weight: 700;
}

.bold-text-2.bold-text-3 {
  color: #fff;
  text-shadow: none;
}

.bold-text-2.bold-text {
  font-size: 34px;
  font-weight: 800;
  text-shadow: 1px 1px 0 rgba(26, 27, 31, 0.15);
}

.bold-text-3 {
  text-shadow: 1px 1px 1px hsla(0, 0%, 100%, 0.5);
}

.bold-text-3.bold-shadow {
  text-shadow: none;
}

.div-block-3 {
  display: none;
  text-align: center;
}

.button-cta {
  margin-top: 0px;
  padding: 40px 80px;
  border-radius: 0px;
  background-color: #ffc461;
  box-shadow: 1px 1px 0 0 rgba(0, 0, 0, 0.4);
  -webkit-transition: color 200ms ease, background-color 200ms ease, -webkit-transform 200ms cubic-bezier(.23, 1, .32, 1);
  transition: color 200ms ease, background-color 200ms ease, -webkit-transform 200ms cubic-bezier(.23, 1, .32, 1);
  transition: transform 200ms cubic-bezier(.23, 1, .32, 1), color 200ms ease, background-color 200ms ease;
  transition: transform 200ms cubic-bezier(.23, 1, .32, 1), color 200ms ease, background-color 200ms ease, -webkit-transform 200ms cubic-bezier(.23, 1, .32, 1);
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #7a510a;
  font-size: 30px;
  line-height: 30px;
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
  text-shadow: 1px 1px 0 hsla(0, 0%, 100%, 0.2);
}

.button-cta:hover {
  background-color: #ffce7a;
  box-shadow: 0 6px 9px 0 rgba(0, 0, 0, 0.2);
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
  color: #584c36;
}

.button-cta.w--current {
  letter-spacing: 1px;
}

.link-3 {
  display: inline-block;
}

.container-center {
  text-align: center;
}

.heading-4 {
  font-family: poynter-oldstyle-display;
  font-size: 55px;
  line-height: 70px;
  font-weight: 700;
}

.text-block-3 {
  margin-bottom: 0.5em;
  opacity: 0.6;
  color: #1a1b1f;
  font-size: 12px;
  line-height: 19px;
}

.select-field {
  display: block;
  margin-bottom: 0px;
}

.text-block-4 {
  display: inline-block;
  font-size: 12px;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.link-4 {
  display: inline-block;
  color: #fff;
}

.link-4:hover {
  color: #fff;
}

.link-5 {
  display: inline-block;
}

.link-6 {
  display: inline-block;
  color: #fff;
}

.avatar {
  width: 48px;
  height: 48px;
  margin-top: 5px;
  margin-right: 5px;
  margin-left: 5px;
  padding: 0px;
  border-radius: 0%;
  background-color: transparent;
  box-shadow: none;
  opacity: 1;
}

.div-block-4 {
  padding-bottom: 0px;
}

.div-block-4.container-blockquote {
  padding-bottom: 40px;
}

.container-blockquote {
  padding-bottom: 40px;
  text-align: center;
}

.text-block-5 {
  color: #1a1b1f;
}

.text-block-6 {
  color: #99702b;
  font-size: 12px;
  line-height: 18px;
  text-align: right;
}

.text-block-6.company-address {
  font-size: 12px;
  line-height: 18px;
  text-align: left;
}

.div-block-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  grid-auto-columns: 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
}

.heading-5 {
  width: 100%;
  margin-top: 0px;
  margin-bottom: 0.5em;
  color: #99702b;
  font-size: 12px;
  font-weight: 400;
}

.company-details {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  margin-top: 100px;
  margin-bottom: 20px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  -ms-flex-align: end;
  align-items: flex-end;
  font-family: niveau-grotesk, sans-serif;
  font-weight: 400;
}

.bold-text-4 {
  font-weight: 500;
}

.div-block-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.bold-text-5 {
  color: rgba(26, 27, 31, 0.6);
  font-weight: 500;
  text-transform: none;
}

.link-7 {
  display: inline-block;
}

.bold-text-6 {
  font-weight: 600;
}

.bold-text-7 {
  font-weight: 600;
}

.paragraph-2 {
  display: block;
  max-width: 36em;
  margin-bottom: 1.618em;
  font-size: 21px;
  line-height: 34px;
  font-weight: 500;
}

.paragraph-2.top-rated {
  font-family: poynter-oldstyle-display;
  font-size: 55px;
  line-height: 77px;
  font-weight: 700;
}

.link-8 {
  display: inline-block;
}

.link-8.bold-text.bold-text-2 {
  -webkit-transition: background-color 200ms ease, opacity 200ms ease;
  transition: background-color 200ms ease, opacity 200ms ease;
}

.link-8.bold-text.bold-text-2:hover {
  color: #cbfdae;
}

.paragraph-3 {
  font-size: 21px;
  line-height: 34px;
}

.paragraph-4 {
  font-size: 55px;
}

.paragraph-5 {
  font-size: 55px;
  line-height: 70px;
}

.grid {
  -webkit-box-pack: stretch;
  -webkit-justify-content: stretch;
  -ms-flex-pack: stretch;
  justify-content: stretch;
  justify-items: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-content: stretch;
  -ms-flex-line-pack: stretch;
  align-content: stretch;
  grid-column-gap: 64px;
  grid-row-gap: 64px;
  -ms-grid-columns: 1fr 1fr 1fr;
  grid-template-columns: 1fr 1fr 1fr;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
}

.portfolio-link {
  display: block;
  padding: 10px;
  -webkit-transition: box-shadow 200ms ease, -webkit-transform 200ms ease;
  transition: box-shadow 200ms ease, -webkit-transform 200ms ease;
  transition: transform 200ms ease, box-shadow 200ms ease;
  transition: transform 200ms ease, box-shadow 200ms ease, -webkit-transform 200ms ease;
  text-align: left;
}

.portfolio-link:hover {
  box-shadow: none;
}

.image-5 {
  padding: 10px;
  background-color: #1a1b1f;
}

.dont-share-link.footer-item.w--current {
  margin-right: 0px;
  margin-left: 0px;
}

.dont-share {
  font-weight: 400;
  letter-spacing: 0px;
  text-transform: none;
}

.text-span-7 {
  color: #fff;
  font-size: 21px;
  line-height: 34px;
  font-weight: 500;
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.25), 1px 1px 12px rgba(0, 0, 0, 0.3);
}

.paragraph-6 {
  margin-bottom: 1em;
  font-family: Montserrat, sans-serif;
  font-size: 34px;
  line-height: 55px;
  font-weight: 700;
  letter-spacing: -1px;
}

.link-9 {
  display: inline-block;
  color: #fff;
}

.link-9.text-span-8 {
  -webkit-transform: translate(0px, 0px);
  -ms-transform: translate(0px, 0px);
  transform: translate(0px, 0px);
  -webkit-transition: background-color 200ms ease, opacity 200ms ease;
  transition: background-color 200ms ease, opacity 200ms ease;
}

.link-9.text-span-8:hover {
  color: #55bdef;
  text-shadow: 1px 1px 0 #fff;
}

.text-span-8 {
  color: #fff;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.32);
}

.text-span-9 {
  color: #fff;
}

.paralax {
  margin-top: 0px;
  margin-right: auto;
  margin-left: auto;
  box-shadow: 1px 1px 20px 0 rgba(0, 0, 0, 0.09), 1px 1px 0 0 rgba(0, 0, 0, 0.06), 1px 1px 6px 0 rgba(0, 0, 0, 0.1);
}

.checkbox {
  display: block;
  margin-top: 7px;
}

.checkbox-label {
  margin-bottom: 0px;
  font-family: 'PT Sans', sans-serif;
  color: rgba(26, 27, 31, 0.64);
  font-size: 14px;
  line-height: 18px;
  letter-spacing: 0px;
  text-transform: none;
}

.link-10 {
  display: inline-block;
}

.loading-bar {
  width: 100%;
  height: 100%;
  background-color: #f1189b;
}

.loading-wrap {
  position: fixed;
  left: 0%;
  top: 0%;
  right: 0%;
  bottom: 0%;
  z-index: 2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: auto;
  height: auto;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #1a1b1f;
}

.loading-bar-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20vw;
  height: 3px;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #454853;
}

.section-6 {
  display: none;
}

.heading-6 {
  font-family: poynter-oldstyle-display;
  color: #a0f2e2;
  font-size: 55px;
  line-height: 89px;
  font-weight: 700;
}

.heading-wrap {
  position: relative;
  display: block;
  overflow: hidden;
  margin-bottom: 0.5em;
  padding-bottom: 0em;
  font-size: 84px;
  line-height: 105px;
}

.heading-load {
  position: absolute;
  left: 0%;
  top: 0%;
  right: 0%;
  bottom: 0%;
  display: none;
  background-color: #fff;
}

.heading-wrap-in {
  font-size: 96px;
  line-height: 105px;
}

.text-span-10 {
  font-size: 96px;
}

.perex {
  display: block;
  margin-bottom: 24px;
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
  font-family: ff-meta-serif-web-pro;
  color: #1a1b1f;
  font-size: 21px;
  line-height: 1.6;
  font-weight: 400;
  text-align: left;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.12);
}

.perex.perex-alt {
  display: block;
  max-width: 30em;
  margin-bottom: 0px;
  color: #771d20;
  text-align: left;
  text-shadow: none;
}

.perex.perex-alt.perex-highlight {
  color: #3d0a0c;
}

.perex.perex-alt.perex-trans {
  max-width: 25em;
  line-height: 36px;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.16);
}

.perex.perex-alt2 {
  display: block;
  color: #fff;
  text-align: left;
  text-shadow: 0 1px 0 hsla(0, 0%, 100%, 0.1);
}

.perex.perex-trans {
  display: block;
  max-width: 30em;
  margin-bottom: 24px;
  color: hsla(0, 0%, 100%, 0.9);
  font-size: 21px;
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.12);
}

.perex.perex-trans.perex-p {
  max-width: 35em;
  margin-bottom: 40px;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  font-size: 30px;
  font-weight: 300;
}

.perex.perex-trans.form-error {
  display: inline;
}

.perex.perex-inline {
  display: inline;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.16);
}

.perex.perex-inline.perex-small {
  color: rgba(37, 46, 39, 0.69);
}

.perex.perex-w {
  width: 100%;
  margin-bottom: 1em;
  -webkit-transition: background-color 200ms ease;
  transition: background-color 200ms ease;
  font-family: niveau-grotesk, sans-serif;
  font-size: 2vw;
  line-height: 160%;
  text-align: center;
}

.perex.perex-small {
  font-size: 20px;
  line-height: 30px;
}

.perex.perex-sans {
  font-family: 'PT Sans', sans-serif;
  font-size: 18px;
}

.h2d {
  margin-top: 0px;
  margin-bottom: 40px;
  color: #fff;
  font-size: 60px;
  line-height: 72px;
  font-weight: 700;
  text-align: center;
  text-shadow: 1px 1px 6px rgba(9, 2, 2, 0.36);
}

.h2-alt {
  display: inline-block;
  color: #f1189b;
  font-size: 60px;
  text-shadow: 2px 2px 0 hsla(0, 0%, 100%, 0.11);
}

.perex-highlight {
  display: inline;
  color: #242e27;
  font-weight: 700;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.16);
}

.perex-highlight.perex {
  margin-bottom: 0px;
}

.perex-highlight.perex-highlight-2 {
  color: #fff;
}

.scroll-wrap {
  background-color: #ee494e;
}

.heading-7 {
  text-align: center;
}

.link-11 {
  display: inline-block;
}

.link-button {
  position: static;
  left: 0%;
  top: 0%;
  right: 0%;
  bottom: 0%;
  display: inline-block;
  margin-right: 2px;
  margin-left: 10px;
  padding: 2px 7px 3px 10px;
  float: none;
  clear: none;
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  grid-auto-columns: 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 2px 2px 1px 0 rgba(26, 27, 31, 0.39);
  -webkit-transition-property: background-color;
  transition-property: background-color;
}

.link-button:hover {
  background-color: #dbf7d2;
}

.heading-8 {
  display: inline-block;
}

.div-block-7 {
  display: block;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
}

.image-6 {
  display: inline-block;
  margin-top: 0px;
}

.div-block-9 {
  display: block;
  padding-bottom: 0.618em;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 24px;
}

.field-label {
  font-family: 'PT Sans', sans-serif;
  color: rgba(26, 27, 31, 0.64);
  font-size: 14px;
  line-height: 14px;
  font-weight: 400;
  letter-spacing: 0px;
  text-transform: none;
}

.text-block-7 {
  text-align: left;
}

.link-12 {
  display: inline;
}

.form-sent {
  position: fixed;
  left: 0%;
  top: 0%;
  right: 0%;
  bottom: 0%;
  z-index: 10;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: 100%;
  padding-right: 20px;
  padding-left: 20px;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  background-color: #ffc461;
  opacity: 1;
}

.form-sent.form-sent-error {
  left: 0%;
  top: 0%;
  right: 0%;
  bottom: 0%;
  background-color: #ee494e;
}

.x-close {
  position: absolute;
  left: auto;
  top: 50px;
  right: 50px;
  bottom: auto;
  font-family: 'Font awesome 5 pro 300', sans-serif;
  color: rgba(24, 10, 34, 0.95);
  font-size: 50px;
  font-weight: 300;
  text-decoration: none;
}

.x-close.w--current {
  position: absolute;
  top: 30px;
  right: 30px;
  -webkit-transition: opacity 200ms ease, -webkit-transform 200ms ease;
  transition: opacity 200ms ease, -webkit-transform 200ms ease;
  transition: transform 200ms ease, opacity 200ms ease;
  transition: transform 200ms ease, opacity 200ms ease, -webkit-transform 200ms ease;
  font-family: 'Font awesome 5 pro 300', sans-serif;
  color: #f0b85a;
  line-height: 55px;
  text-decoration: none;
}

.x-close.w--current:hover {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
  color: #c99848;
}

.x-close.x-close-error.w--current {
  color: #cf3d42;
}

.x-close.x-close-error.w--current:hover {
  color: #b63337;
}

.button-ico {
  display: block;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  border-radius: 4px;
  font-size: 21px;
  font-weight: 600;
  text-transform: none;
}

.text-span-11 {
  font-family: 'Font awesome 5 pro 300', sans-serif;
  font-size: 21px;
  line-height: 22px;
  font-weight: 300;
}

.text-span-12 {
  color: #242e27;
  font-weight: 700;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.16);
}

.div-block-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.div-block-11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.div-block-12 {
  position: relative;
  display: inline-block;
  height: 30px;
  line-height: 30px;
}

.image-desc {
  padding-left: 4px;
  color: rgba(37, 46, 39, 0.49);
  font-size: 12px;
  font-style: italic;
}

.div-block-13 {
  position: relative;
  z-index: 10;
}

.image-7 {
  margin-right: 32px;
  float: left;
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
  border-radius: 50%;
  box-shadow: 0 4px 3px 2px rgba(79, 33, 111, 0.33), 0 0 0 4px #9039cc;
  -webkit-transform: rotate(-31deg);
  -ms-transform: rotate(-31deg);
  transform: rotate(-31deg);
}

.container-5 {
  display: block;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.paragraph-7 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
}

.image-8 {
  margin-right: 0px;
  float: none;
  box-shadow: none;
  -webkit-transform: rotate(-40deg);
  -ms-transform: rotate(-40deg);
  transform: rotate(-40deg);
}

.profile-img {
  display: inline-block;
  margin-top: 8px;
  margin-right: 32px;
  padding: 12px;
  float: none;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: start;
  align-self: flex-start;
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  border-radius: 50%;
  background-color: #9039cc;
  background-image: linear-gradient(135deg, #9039cc, #f1189b);
  box-shadow: 1px 1px 12px 0 rgba(87, 34, 124, 0.27);
}

.div-block-14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.column {
  padding-right: 48px;
  padding-left: 48px;
}

.link-13 {
  display: inline;
  color: #fff;
}

.link-13:hover {
  color: #fff;
  text-shadow: none;
}

.h3 {
  margin-top: 0px;
  margin-bottom: 32px;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #fff;
  font-size: 42px;
  line-height: 1.3;
  font-weight: 700;
}

.h3.h3-c {
  text-align: center;
}

.h3.h3-alt {
  color: rgba(0, 0, 0, 0.8);
}

.container-6 {
  display: block;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-auto-columns: 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
}

.avocado-logo {
  margin-right: 0px;
  float: left;
  -webkit-align-self: auto;
  -ms-flex-item-align: auto;
  -ms-grid-row-align: auto;
  align-self: auto;
  -webkit-box-ordinal-group: 1;
  -webkit-order: 0;
  -ms-flex-order: 0;
  order: 0;
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
}

.heading-9 {
  margin-top: 0px;
  margin-bottom: 8px;
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
  font-family: cronos-pro-caption;
  color: #140f0d;
  font-size: 36px;
  line-height: 58px;
  font-weight: 400;
  text-shadow: none;
}

.heading-10 {
  font-family: cronos-pro-caption;
  font-size: 48px;
  font-weight: 300;
}

.link-block {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-top: 112px;
  margin-bottom: 112px;
  margin-left: 0px;
  padding-top: 40px;
  padding-bottom: 40px;
  padding-left: 16px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 16px;
  background-color: #c1f121;
  -webkit-transition: background-color 150ms cubic-bezier(.215, .61, .355, 1);
  transition: background-color 150ms cubic-bezier(.215, .61, .355, 1);
  text-decoration: none;
}

.link-block:hover {
  background-color: #ccff24;
  box-shadow: 1px 1px 2px 0 rgba(0, 0, 0, 0.1), 6px 6px 24px 0 rgba(83, 54, 1, 0.1);
}

.div-block-15 {
  display: inline-block;
  height: 100%;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}

.avocado-logo-wrap {
  display: inline-block;
  margin-right: 24px;
}

.columns {
  max-width: 1296px;
  margin-right: auto;
  margin-left: auto;
  padding-right: 0px;
  padding-left: 0px;
}

.hl {
  color: #ffc461;
  text-shadow: 0 0 20px rgba(41, 67, 172, 0.58);
}

.bold-text-8 {
  font-size: 36px;
  font-weight: 400;
  text-shadow: 2px 2px 1px hsla(0, 0%, 100%, 0.2);
}

.button-3 {
  background-color: #f1189b;
  -webkit-transition-property: background-color;
  transition-property: background-color;
  font-family: open-sans;
  color: #fff;
  font-size: 13px;
  font-weight: 600;
  text-transform: none;
}

.button-3:hover {
  background-color: #62b1ff;
  color: #000;
}

.button-3.button-desktop {
  padding: 8px 16px;
  border-style: solid;
  border-width: 2px;
  border-color: #140f0d;
  border-radius: 4px;
  background-color: transparent;
  box-shadow: 1px 1px 0 1px hsla(0, 0%, 100%, 0.3);
  -webkit-transition: border-color 200ms ease, color 200ms ease, background-color 200ms ease;
  transition: border-color 200ms ease, color 200ms ease, background-color 200ms ease;
  font-family: Oswald, sans-serif;
  color: #140f0d;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  letter-spacing: 1px;
  text-shadow: 1px 1px 0 hsla(0, 0%, 100%, 0.2);
}

.button-3.button-desktop:hover {
  border-color: #a320e3;
  color: #a320e3;
}

.button-3.button-desktop.w--current {
  border-color: #3a490a;
  color: #3a490a;
}

.button-3.button-desktop {
  padding: 8px 12px;
  border-radius: 4px;
  background-color: #62b1ff;
  -webkit-transition: color 200ms ease, background-color 200ms ease, -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  transition: color 200ms ease, background-color 200ms ease, -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  transition: transform 200ms cubic-bezier(.165, .84, .44, 1), color 200ms ease, background-color 200ms ease;
  transition: transform 200ms cubic-bezier(.165, .84, .44, 1), color 200ms ease, background-color 200ms ease, -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #180a22;
  font-size: 14px;
  line-height: 21px;
  font-weight: 700;
  letter-spacing: 1px;
}

.button-3.button-desktop:hover {
  background-color: #62b1ff;
  -webkit-transform: scale(1.05);
  -ms-transform: scale(1.05);
  transform: scale(1.05);
}

.social-link {
  display: inline-block;
  color: #fff;
}

.nav-link.w--current {
  display: none;
  margin-right: 40px;
  font-family: Oswald, sans-serif;
  color: #3a490a;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  text-decoration: none;
  text-transform: uppercase;
  text-shadow: 1px 1px 0 hsla(0, 0%, 100%, 0.2);
}

.social-list {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-top: 0px;
  margin-bottom: 0px;
  padding-left: 0px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  grid-auto-columns: 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
  list-style-type: none;
}

.social-ico {
  display: inline-block;
  width: auto;
  height: 18px;
  -webkit-transition: opacity 200ms ease;
  transition: opacity 200ms ease;
}

.social-ico:hover {
  opacity: 0.6;
  color: #a320e3;
}

.social-ico.social-ico-dark {
  opacity: 0.8;
}

.social-ico.social-ico-dark:hover {
  opacity: 1;
}

.list-item {
  margin-bottom: 0px;
  margin-left: 12px;
  padding-bottom: 0px;
  -webkit-align-self: auto;
  -ms-flex-item-align: auto;
  -ms-grid-row-align: auto;
  align-self: auto;
  list-style-type: none;
}

.list-item.dribbble {
  display: none;
}

.link-14 {
  display: inline-block;
  color: #fff;
}

.link-15 {
  display: inline;
}

.link-16 {
  display: block;
  color: #fff;
}

.link-17 {
  padding-top: 16px;
  padding-bottom: 16px;
  text-align: center;
  text-decoration: none;
}

.bold-text-9 {
  font-family: 'PT Sans', sans-serif;
  color: rgba(26, 27, 31, 0.55);
  font-weight: 400;
  letter-spacing: 3px;
  text-decoration: none;
}

.stars {
  margin-top: 40px;
  text-align: center;
}

.image-9 {
  box-shadow: 1px -1px 3px 0 #fff;
}

.image-10 {
  width: 100%;
}

.link-block-2 {
  width: 100%;
  text-align: left;
  text-decoration: none;
}

.section-7 {
  padding-top: 60px;
  padding-bottom: 60px;
  border: 60px solid #336178;
  background-color: #1e4355;
}

.block-quote-2 {
  border-left-style: none;
  border-left-width: 0px;
  font-family: cronos-pro-caption;
  color: #fff;
  font-size: 46px;
  line-height: 1.2em;
  font-style: normal;
  font-weight: 300;
  text-decoration: none;
}

.heading-11 {
  font-family: cronos-pro-caption;
  color: #fff;
  font-size: 54px;
  font-weight: 700;
  text-align: center;
}

.text-block-8 {
  font-family: 'Font awesome 5 pro 300', sans-serif;
}

.star {
  display: inline-block;
  margin-right: 2px;
  margin-left: 2px;
}

.text-block-9 {
  font-family: cronos-pro-caption;
  color: #fff;
  font-size: 28px;
  line-height: 32px;
  font-weight: 300;
  text-align: center;
}

.image-11 {
  margin-top: 12px;
  padding-right: 18px;
  float: right;
}

.what-i-do {
  display: none;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.service-img {
  display: inline-block;
}

.service-col {
  padding-right: 20px;
  padding-left: 20px;
}

.service-cols {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-right: -20px;
  margin-left: -20px;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.service-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 500px;
  margin-right: auto;
  margin-left: auto;
  padding: 40px;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  border: 8px none #b5e220;
  border-radius: 0px;
  background-color: hsla(0, 0%, 100%, 0.95);
  background-image: linear-gradient(135deg, #ffde0a, #b5e220);
  box-shadow: 6px 6px 0 0 #ffde0a;
  -webkit-transition: background-color 200ms ease, opacity 200ms ease, -webkit-transform 200ms ease;
  transition: background-color 200ms ease, opacity 200ms ease, -webkit-transform 200ms ease;
  transition: transform 200ms ease, background-color 200ms ease, opacity 200ms ease;
  transition: transform 200ms ease, background-color 200ms ease, opacity 200ms ease, -webkit-transform 200ms ease;
}

.service-link:hover {
  border-color: #ffde0a;
  background-color: #fff;
  background-image: linear-gradient(135deg, #b5e220, #ffde0a);
  box-shadow: 3px 3px 0 0 #b5e220;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}

.service-p {
  margin-bottom: 3em;
  font-family: ff-meta-serif-web-pro;
  color: #180a22;
  font-size: 30px;
  line-height: 1.6;
  font-weight: 300;
  text-shadow: 1px 1px 0 rgba(24, 10, 34, 0.09);
}

.list-item-2 {
  margin-bottom: 0px;
  margin-left: 12px;
  padding-bottom: 0px;
  -webkit-align-self: auto;
  -ms-flex-item-align: auto;
  -ms-grid-row-align: auto;
  align-self: auto;
  list-style-type: none;
}

.social-link-2 {
  display: inline-block;
}

.social-ico-2 {
  display: inline-block;
  height: 24px;
  -webkit-transition: opacity 200ms ease;
  transition: opacity 200ms ease;
}

.social-ico-2:hover {
  opacity: 0.6;
  color: #a320e3;
}

.tags {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0em -40px;
  padding-top: 5.5em;
  padding-bottom: 6em;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-justify-content: space-around;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #9039cc;
}

.tag {
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
  -webkit-transition: -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  transition: -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  transition: transform 200ms cubic-bezier(.165, .84, .44, 1);
  transition: transform 200ms cubic-bezier(.165, .84, .44, 1), -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #fff;
  font-size: 40px;
  line-height: 1.2;
  font-weight: 700;
  text-align: left;
  text-shadow: none;
}

.b {
  font-weight: 700;
}

.thinking-bold {
  color: #fff;
}

.faq-bold.h2-alt {
  display: inline;
  color: #a70cf2;
  font-size: 24px;
  font-weight: 700;
}

.highlight {
  color: #ffea62;
}

.service-p-2 {
  margin-bottom: 2em;
  font-family: niveau-grotesk, sans-serif;
  color: #180a22;
  font-size: 24px;
  line-height: 1.6;
  font-weight: 300;
  text-shadow: 1px 1px 0 rgba(24, 10, 34, 0.09);
}

.paragraph-tiny-2 {
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  font-size: 12px;
  line-height: 18px;
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: none;
}

.paragraph-tiny-2.cc-paragraph-tiny-light {
  opacity: 1;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #f9f0ff;
  font-size: 20px;
  line-height: 30px;
  font-weight: 700;
  letter-spacing: 0em;
  text-decoration: none;
  text-transform: none;
}

.navigation-2 {
  z-index: 1;
  display: block;
  width: 100%;
  max-width: 1400px;
  margin-right: auto;
  margin-left: auto;
  padding: 20px 30px 20px 10px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-ordinal-group: 1;
  -webkit-order: 0;
  -ms-flex-order: 0;
  order: 0;
  background-color: transparent;
}

.navigation-wrap-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-right: 0px;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.link-18 {
  display: inline-block;
}

.star-2 {
  display: inline-block;
  margin-right: 2px;
  margin-left: 2px;
}

.image-12 {
  display: inline-block;
  margin-right: 10px;
  border-radius: 100%;
}

.mask {
  position: relative;
  overflow: hidden;
  margin-right: 40px;
  margin-left: 40px;
}

.container-7 {
  display: block;
  width: 100%;
  max-width: 1500px;
  margin: 0px auto;
  padding: 0px 120px;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  grid-auto-columns: 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  -ms-grid-columns: auto 1fr;
  grid-template-columns: auto 1fr;
  -ms-grid-rows: auto auto;
  grid-template-rows: auto auto;
}

.container-7.container-testimonials {
  max-width: 75em;
}

.container-7.points {
  padding-top: 80px;
  padding-right: 85px;
  padding-left: 85px;
}

.blockquote-author-2 {
  display: block;
  margin-right: 32px;
  margin-left: 32px;
}

.slider {
  width: 100%;
  height: 500px;
  background-color: transparent;
}

.block-quote-3 {
  width: 100%;
  margin-bottom: 16px;
  padding: 16px 32px;
  border-left-style: none;
  font-family: niveau-grotesk, sans-serif;
  color: #f5f0f6;
  font-size: 30px;
  line-height: 51px;
  font-weight: 400;
  text-align: left;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.36);
}

.container-blockquote-2 {
  display: block;
  height: 100%;
  padding-bottom: 40px;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  text-align: left;
}

.slide-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 64px;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 0px;
  -webkit-transition: background-color 200ms ease;
  transition: background-color 200ms ease;
}

.slide-link:hover {
  background-color: hsla(0, 0%, 100%, 0.1);
}

.slide-link.slide-link--case:hover {
  background-color: rgba(144, 57, 204, 0.88);
}

.icon-2 {
  margin-right: auto;
  margin-left: auto;
  -webkit-filter: invert(100%);
  filter: invert(100%);
  color: rgba(0, 0, 0, 0.24);
}

.text-block-10 {
  display: inline-block;
  padding-top: 8px;
  padding-bottom: 8px;
  font-family: niveau-grotesk-small-caps, sans-serif;
  color: #f5f0f6;
  font-size: 14px;
  line-height: 21px;
  font-weight: 400;
  text-transform: uppercase;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}

.slide-nav {
  position: absolute;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 40px;
  padding-top: 8px;
  padding-bottom: 0px;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 4px;
  background-color: transparent;
}

.slide-nav.slide-nav--case {
  background-color: hsla(0, 0%, 100%, 0.7);
}

.icon {
  margin-right: 16px;
  margin-left: 16px;
  color: rgba(26, 27, 31, 0.39);
}

.icon.icon-2 {
  -webkit-filter: invert(100%);
  filter: invert(100%);
}

.max-w {
  max-width: 720px;
}

.section-p-2 {
  padding-top: 50px;
  padding-bottom: 60px;
  background-color: #f2f2f2;
}

.section-p-2.section-who {
  padding-top: 100px;
  padding-bottom: 100px;
  background-color: #fff;
}

.section-p-2.section-who.section-map {
  background-color: #161617;
}

.section-p-2.section-me {
  overflow: hidden;
  padding-top: 100px;
  padding-bottom: 120px;
  background-color: transparent;
}

.avo-clients-map {
  width: 100vw;
  margin-top: 40px;
}

.h2-2 {
  display: block;
  margin-top: 0px;
  margin-bottom: 40px;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #fff;
  font-size: 60px;
  line-height: 72px;
  font-weight: 700;
  text-align: left;
  letter-spacing: 0px;
  text-shadow: 2px 2px 1px rgba(26, 27, 31, 0.39);
}

.h2-2.h1-c {
  max-width: 20em;
  margin-right: auto;
  margin-left: auto;
  color: #242614;
  line-height: 72px;
  text-align: center;
  text-shadow: 2px 2px 1px rgba(26, 27, 31, 0.15);
}

.h2-2.h1-c.light {
  color: #fff;
  text-align: center;
}

.h2-2.h1-c.smaller {
  max-width: none;
  color: rgba(24, 10, 34, 0.28);
  font-size: 30px;
  font-weight: 300;
  text-shadow: none;
}

.h2-2.h1-c.smaller.dark {
  color: #180a22;
}

.h2-2.h3 {
  margin-bottom: 16px;
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #1a1b1f;
  font-size: 37px;
  line-height: 1.5em;
  font-weight: 300;
  text-shadow: 2px 2px 1px rgba(26, 27, 31, 0.06);
}

.h2-2.h3.h-col {
  margin-bottom: 0.618em;
  font-size: 30px;
  line-height: 1.3em;
  font-weight: 700;
}

.perex-2 {
  display: block;
  margin-bottom: 24px;
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
  font-family: Montserrat, sans-serif;
  color: #140f0d;
  font-size: 20px;
  line-height: 30px;
  font-weight: 400;
  text-align: left;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.12);
}

.perex-2.perex-c {
  margin-bottom: 0px;
  color: #505242;
  font-size: 19px;
  line-height: 1.5em;
  text-align: center;
}

.perex-2.perex-c.light {
  max-width: 40em;
  margin-right: auto;
  margin-left: auto;
  font-family: niveau-grotesk, sans-serif;
  color: #e3eaf1;
  font-size: 24px;
  font-weight: 400;
  text-align: left;
}

.perex-2.perex-c.light.c {
  text-align: center;
}

.perex-2.perex-smaller {
  display: block;
  font-family: niveau-grotesk, sans-serif;
  color: #1a1b1f;
  font-size: 20px;
  line-height: 1.6;
  font-weight: 400;
  text-shadow: none;
}

.casestudy {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  font-family: 'PT Sans', sans-serif;
  font-size: 15px;
  line-height: 1.5;
  font-weight: 300;
}

.casestudy.casestudy--c {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  padding-top: 80px;
  padding-bottom: 80px;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.case-title {
  margin-top: 50px;
  margin-bottom: 0px;
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 100%;
  -ms-flex: 1 0 100%;
  flex: 1 0 100%;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #fff;
  font-size: 30px;
  line-height: 1.5;
  font-weight: 700;
}

.case-title.case-title--c {
  margin-bottom: 2em;
  text-align: center;
}

.case-title.case-title--c.dark {
  color: #180a22;
}

.text-block-11 {
  font-size: 20px;
}

.case-year {
  font-family: niveau-grotesk, sans-serif;
  color: #e7deee;
  font-weight: 400;
}

.case-h {
  margin-top: 2em;
  margin-bottom: 0.2em;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #ffde0a;
  font-size: 15px;
  line-height: 1.5;
  font-weight: 700;
  text-transform: uppercase;
}

.case-h.case-h__client {
  font-family: ff-meta-serif-web-pro;
  color: #180a22;
  font-weight: 300;
  text-transform: none;
}

.slider-2 {
  height: 500px;
}

.casestudy-slider {
  height: 850px;
  margin-top: 50px;
  background-color: transparent;
}

.case-c.case-c__desc {
  margin-right: 5em;
  margin-left: 40px;
  -webkit-box-flex: 0;
  -webkit-flex: 0 19em;
  -ms-flex: 0 19em;
  flex: 0 19em;
}

.case-c.case-c__img {
  -webkit-box-flex: 66%;
  -webkit-flex: 66%;
  -ms-flex: 66%;
  flex: 66%;
  background-color: #fff;
}

.case__client {
  margin-top: 0px;
  margin-bottom: 0px;
  font-family: niveau-grotesk, sans-serif;
  color: #f9f0ff;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
}

.case-list {
  margin-top: 0px;
  margin-bottom: 0px;
  padding-left: 0px;
  list-style-type: none;
}

.case-li {
  margin-bottom: 0px;
  font-family: niveau-grotesk, sans-serif;
  color: #f9f0ff;
  font-size: 16px;
  font-weight: 400;
}

.btn-portfolio {
  margin-bottom: 0.5em;
  padding: 1.3em 3em;
  border-radius: 6px;
  background-image: linear-gradient(328deg, #0365c0, #9039cc);
  box-shadow: 1px 1px 8px 0 rgba(0, 0, 0, 0.19);
  -webkit-transform: translate(0px, 0px);
  -ms-transform: translate(0px, 0px);
  transform: translate(0px, 0px);
  -webkit-transition: opacity 200ms ease;
  transition: opacity 200ms ease;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  font-size: 20px;
  font-weight: 700;
}

.btn-portfolio:hover {
  -webkit-transform: translate(0px, 1px);
  -ms-transform: translate(0px, 1px);
  transform: translate(0px, 1px);
  color: #fff;
}

.btn-portfolio.btn--webflow {
  background-image: linear-gradient(328deg, #9039cc, #111);
}

.font-ico {
  font-family: 'Font awesome brands', sans-serif;
  font-weight: 300;
}

.btns {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.clients {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-width: 1000px;
  margin: 80px auto;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.client-img {
  margin-right: 50px;
  margin-bottom: 60px;
  margin-left: 50px;
  opacity: 0.66;
}

.section-tags {
  display: block;
  padding-right: 40px;
  padding-left: 40px;
}

.columns-2 {
  margin-right: -40px;
  margin-left: -40px;
}

.column-2 {
  display: block;
  padding-right: 24px;
  padding-left: 24px;
}

.column-2.col {
  padding-right: 30px;
  padding-left: 30px;
}

.hell-p {
  margin-bottom: 0.6em;
}

.hero-img {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}

.hero-img__wrap {
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 400px;
  -ms-flex: 1 0 400px;
  flex: 1 0 400px;
  border-width: 10px;
  border-color: #ffde0a;
  border-radius: 100%;
  background-color: #ffde0a;
  background-clip: border-box;
}

.nav {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-top: 0px;
  margin-bottom: 0px;
  padding-left: 0px;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  list-style-type: none;
}

.nav-a {
  margin-left: 2em;
  font-family: niveau-grotesk, sans-serif;
  color: #f9f0ff;
  font-size: 20px;
  font-weight: 400;
  text-decoration: none;
}

.nav-a:hover {
  color: #ffde0a;
  text-decoration: underline;
}

.list-item-3 {
  margin-left: 5em;
}

.nav-li {
  margin-bottom: 0px;
}

.paragraph-8 {
  font-family: niveau-grotesk, sans-serif;
}

.case-p {
  color: #f9f0ff;
  font-size: 16px;
  font-weight: 400;
}

.link-block-3 {
  text-decoration: none;
}

.link-19 {
  display: inline-block;
}

.heading-jumbo-2 {
  position: static;
  left: -500px;
  display: inline-block;
  margin-top: 10px;
  margin-bottom: 1em;
  color: #ffed61;
  font-size: 55px;
  line-height: 89px;
  font-weight: 700;
  text-transform: none;
  text-shadow: 1px 1px 1px #000;
}

.heading-jumbo-2.heading-gradient {
  display: block;
  margin-top: 0px;
  margin-bottom: 0em;
  padding-bottom: 8px;
  direction: ltr;
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #180a22;
  font-size: 60px;
  line-height: 1.2;
  font-weight: 700;
  text-align: left;
  text-transform: none;
  text-shadow: none;
}

.tags-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0em -40px;
  padding-top: 5.5em;
  padding-bottom: 6em;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-justify-content: space-around;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #9039cc;
}

.tag-2 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 auto;
  -ms-flex: 0 auto;
  flex: 0 auto;
  -webkit-transition: -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  transition: -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  transition: transform 200ms cubic-bezier(.165, .84, .44, 1);
  transition: transform 200ms cubic-bezier(.165, .84, .44, 1), -webkit-transform 200ms cubic-bezier(.165, .84, .44, 1);
  font-family: itc-avant-garde-gothic-pro, sans-serif;
  color: #fff;
  font-size: 30px;
  line-height: 1.2;
  font-weight: 700;
  text-align: left;
  text-shadow: none;
}

.tag-2:hover {
  -webkit-transform: scale(1.2);
  -ms-transform: scale(1.2);
  transform: scale(1.2);
}

@media screen and (min-width: 1280px) {
  .case-c.case-c__img {
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
  }
}

@media screen and (min-width: 1440px) {
  .heading-jumbo.heading-gradient {
    color: #fff;
  }

  .sub-heading {
    font-size: 28px;
  }

  .section-p.section-work-together {
    background-color: #222;
  }

  .h2 {
    font-size: 66px;
  }

  .perex.perex-trans.perex-p {
    font-size: 30px;
  }

  .service-p {
    line-height: 1.7;
  }

  .service-p-2 {
    line-height: 1.7;
  }

  .perex-2.perex-c.light {
    font-size: 24px;
  }

  .case-c.case-c__img {
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    -webkit-flex-basis: 66%;
    -ms-flex-preferred-size: 66%;
    flex-basis: 66%;
  }
}

@media screen and (max-width: 991px) {
  .styleguide-block {
    text-align: center;
  }

  .container {
    display: block;
    height: auto;
    margin-top: 0px;
    margin-bottom: 0px;
    padding-top: 0px;
    padding-bottom: 0px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }

  .container.container-header {
    margin-top: 0px;
    margin-bottom: 0px;
    padding-top: 10vh;
    padding-bottom: 10vh;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
  }

  .container.container-contact {
    margin-top: 0px;
    margin-bottom: 0px;
    padding-top: 40px;
    padding-bottom: 40px;
  }

  .container.grid {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding: 60px 0px;
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    grid-column-gap: 0px;
    grid-template-areas: ".";
    -ms-grid-columns: auto;
    grid-template-columns: auto;
  }

  .heading-jumbo {
    margin-bottom: 1em;
    font-size: 42px;
    line-height: 67px;
  }

  .heading-jumbo.heading-gradient {
    padding-bottom: 8px;
    font-size: 40px;
  }

  .paragraph-tiny {
    display: inline-block;
    color: #1a1b1f;
  }

  .section {
    display: block;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    justify-items: stretch;
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    -webkit-align-content: space-around;
    -ms-flex-line-pack: distribute;
    align-content: space-around;
    grid-auto-columns: 1fr;
    grid-column-gap: 0px;
    grid-row-gap: 0px;
    -ms-grid-columns: auto;
    grid-template-columns: auto;
    -ms-grid-rows: 1fr auto 2fr;
    grid-template-rows: 1fr auto 2fr;
    background-position: 100% 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-attachment: scroll;
  }

  .section.cc-contact {
    padding: 96px 40px;
  }

  .section.hero {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    min-height: auto;
    white-space: normal;
  }

  .section.section-bg {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .button {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
  }

  .logo-link.w--current {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
  }

  .menu-icon {
    display: block;
  }

  .navigation-item {
    padding: 15px 30px;
    -webkit-transition: background-color 400ms ease, opacity 400ms ease, color 400ms ease;
    transition: background-color 400ms ease, opacity 400ms ease, color 400ms ease;
    text-align: center;
  }

  .navigation-item:hover {
    background-color: #f7f8f9;
  }

  .navigation-item:active {
    background-color: #eef0f3;
  }

  .navigation-items {
    background-color: transparent;
  }

  .navigation {
    padding: 25px 30px;
  }

  .menu-button {
    padding: 0px;
  }

  .menu-button.w--open {
    background-color: transparent;
  }

  .navigation-wrap {
    margin-right: 0px;
  }

  .intro-wrap {
    display: block;
    height: 100%;
    margin-bottom: 0px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }

  .work-experience-grid {
    grid-template-areas: ". ."
      ". .";
    -ms-grid-columns: 1fr 1fr;
    grid-template-columns: 1fr 1fr;
    -ms-grid-rows: auto auto;
    grid-template-rows: auto auto;
  }

  .works-grid {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
  }

  .carrer-headline-wrap {
    width: auto;
  }

  .work-image {
    margin-bottom: 30px;
  }

  .contact {
    width: auto;
    padding: 30px 50px 40px;
  }

  .contact-headline {
    width: 100%;
  }

  .contact-form-grid {
    -webkit-box-pack: stretch;
    -webkit-justify-content: stretch;
    -ms-flex-pack: stretch;
    justify-content: stretch;
  }

  .contact-form-wrap {
    width: 100%;
  }

  .footer-wrap {
    display: block;
    padding-right: 40px;
    padding-left: 40px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .webflow-link.w--current {
    display: inline-block;
    margin-right: 0px;
    margin-bottom: 0px;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .footer-links {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-right: 0px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
  }

  .footer-item {
    margin-right: 12px;
    margin-left: 0px;
    color: #1a1b1f;
  }

  .about-intro-grid {
    grid-row-gap: 50px;
    grid-template-areas: "."
      ".";
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    -ms-grid-rows: auto 50px auto;
    grid-template-rows: auto auto;
    text-align: center;
  }

  .about-head-text-wrap {
    width: 80%;
    margin-right: auto;
    margin-left: auto;
  }

  .service-name-text {
    font-size: 24px;
    line-height: 42px;
  }

  .skillset-wrap {
    padding-right: 0px;
  }

  .services-items-grid {
    padding-top: 0px;
    grid-row-gap: 0px;
    grid-template-areas: "."
      ".";
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    -ms-grid-rows: auto 0px auto;
    grid-template-rows: auto auto;
  }

  .skills-grid {
    width: 80%;
    margin-right: auto;
    margin-left: auto;
    grid-row-gap: 50px;
    grid-template-areas: "."
      ".";
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    -ms-grid-rows: auto 50px auto;
    grid-template-rows: auto auto;
    text-align: center;
  }

  .personal-features-grid {
    width: 80%;
    margin-right: auto;
    margin-left: auto;
    grid-template-areas: "."
      "."
      "."
      ".";
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    -ms-grid-rows: auto auto auto auto;
    grid-template-rows: auto auto auto auto;
    text-align: center;
  }

  .social-media-heading {
    width: 80%;
    margin-right: auto;
    margin-left: auto;
    text-align: center;
  }

  .social-media-grid {
    grid-template-areas: ". ."
      ". ."
      ". ."
      ". .";
    -ms-grid-columns: 1fr 1fr;
    grid-template-columns: 1fr 1fr;
    -ms-grid-rows: auto auto auto auto;
    grid-template-rows: auto auto auto auto;
  }

  .project-overview-grid {
    width: 70%;
    margin-right: auto;
    margin-left: auto;
    grid-row-gap: 50px;
    grid-template-areas: "."
      "."
      ".";
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    -ms-grid-rows: auto 50px auto 50px auto;
    grid-template-rows: auto auto auto;
    text-align: center;
  }

  .project-description-grid {
    width: 80%;
    margin-right: auto;
    margin-left: auto;
    grid-row-gap: 50px;
    grid-template-areas: "."
      ".";
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    -ms-grid-rows: auto 50px auto;
    grid-template-rows: auto auto;
    text-align: center;
  }

  .email-section {
    margin-bottom: 160px;
  }

  .email-link {
    font-size: 36px;
    line-height: 54px;
  }

  .body {
    background-color: transparent;
    background-image: none;
  }

  .image {
    display: inline-block;
  }

  .sub-heading {
    position: relative;
    margin-top: 0px;
    margin-bottom: 30px;
    text-align: left;
  }

  .sub-heading.hero-sub {
    max-width: 15em;
  }

  .block-quote {
    font-size: 24px;
    line-height: 1.6;
  }

  .section-3 {
    padding-top: 80px;
    padding-bottom: 80px;
    background-color: #fff;
  }

  .heading-3 {
    font-size: 55px;
    line-height: 89px;
  }

  .paragraph.paragraph-bigger {
    font-size: 21px;
    line-height: 34px;
  }

  .section-p.section-avocado {
    padding-top: 72px;
    padding-bottom: 72px;
  }

  .section-p.section-upwork {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .section-p.section-how {
    padding-top: 100px;
    padding-bottom: 100px;
  }

  .section-p.section-work-together {
    padding-top: 96px;
    padding-bottom: 96px;
  }

  .section-p.section-bg {
    padding: 96px 24px;
  }

  .section-p.section-light {
    padding-top: 72px;
    padding-bottom: 72px;
  }

  .section-p.section-temp {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .h2 {
    line-height: 1.2;
    text-align: left;
  }

  .h2.h1-smaller {
    font-size: 72px;
    line-height: 79px;
  }

  .h2.h1-w {
    padding-top: 0.618em;
    padding-bottom: 0.125em;
  }

  .h2.h3.h3-c.h3-light {
    font-size: 20px;
  }

  .h2.h2-light {
    font-size: 40px;
  }

  .button-cta {
    line-height: 27px;
  }

  .button-cta.w--current {
    padding-top: 16px;
    padding-bottom: 16px;
    font-size: 18px;
    line-height: 27px;
  }

  .container-center {
    padding-right: 40px;
    padding-left: 40px;
  }

  .container-2 {
    padding-right: 40px;
    padding-left: 40px;
  }

  .container-3 {
    padding-right: 40px;
    padding-left: 40px;
  }

  .container-4 {
    padding-right: 40px;
    padding-left: 40px;
  }

  .text-block-6 {
    text-align: left;
  }

  .text-block-6.company-address {
    margin-bottom: 1em;
  }

  .company-details {
    clear: none;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }

  .div-block-6 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 40px;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .dont-share-link.footer-item.w--current {
    float: right;
  }

  .paralax {
    position: relative;
    z-index: 10;
    width: auto;
    margin-right: 0px;
    margin-left: 0px;
    box-shadow: none;
  }

  .heading-wrap {
    margin-bottom: 32px;
  }

  .perex.perex-alt.perex-trans {
    font-size: 20px;
    line-height: 30px;
  }

  .perex.perex-trans {
    font-size: 21px;
  }

  .perex.perex-trans.perex-p {
    font-size: 24px;
  }

  .perex.perex-w {
    font-size: 2.5vw;
  }

  .perex.perex-sans {
    font-size: 16px;
  }

  .div-block-8 {
    padding-right: 40px;
    padding-left: 40px;
    -webkit-box-ordinal-group: 0;
    -webkit-order: -1;
    -ms-flex-order: -1;
    order: -1;
  }

  .div-block-13 {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
  }

  .container-5 {
    padding-right: 0px;
    padding-left: 0px;
  }

  .profile-img {
    margin-right: 24px;
    padding: 5px;
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 30%;
    -ms-flex: 0 0 30%;
    flex: 0 0 30%;
  }

  .column {
    padding-right: 16px;
    padding-left: 16px;
  }

  .h3 {
    font-size: 30px;
    line-height: 36px;
  }

  .heading-9 {
    margin-bottom: 8px;
    font-size: 24px;
    line-height: 36px;
  }

  .link-block {
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .div-block-15 {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
  }

  .avocado-logo-wrap {
    width: auto;
    margin-right: 32px;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
  }

  .columns {
    padding-right: 0px;
    padding-left: 0px;
  }

  .bold-text-8 {
    font-size: 36px;
  }

  .section-7 {
    border-width: 40px;
  }

  .block-quote-2 {
    font-size: 42px;
  }

  .heading-11 {
    font-size: 48px;
  }

  .image-11 {
    height: 30px;
    margin-right: 18px;
  }

  .service-link {
    width: 350px;
  }

  .service-p {
    font-size: 24px;
  }

  .tags {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-top: 0em;
    padding-bottom: 4em;
  }

  .tag {
    margin-bottom: 1em;
    text-shadow: none;
  }

  .tag:hover {
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }

  .service-p-2 {
    font-size: 24px;
  }

  .paragraph-tiny-2 {
    display: inline-block;
    color: #1a1b1f;
  }

  .navigation-2 {
    padding: 25px 30px;
  }

  .navigation-wrap-2 {
    margin-right: 0px;
  }

  .container-7 {
    display: block;
    height: auto;
    margin-top: 0px;
    margin-bottom: 0px;
    padding: 0px 40px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }

  .container-7.container-testimonials {
    padding-right: 0px;
    padding-left: 0px;
  }

  .container-7.points {
    padding-right: 0px;
    padding-left: 0px;
  }

  .block-quote-3 {
    font-size: 24px;
    line-height: 36px;
  }

  .text-block-10 {
    font-size: 13px;
  }

  .section-p-2.section-me {
    padding-top: 80px;
  }

  .h2-2 {
    font-size: 45px;
    line-height: 58px;
    text-align: left;
  }

  .h2-2.h1-c {
    font-size: 40px;
    line-height: 48px;
  }

  .h2-2.h3.h-col {
    font-size: 24px;
  }

  .perex-2 {
    font-size: 16px;
    line-height: 24px;
  }

  .perex-2.perex-c {
    max-width: 30em;
    margin-right: auto;
    margin-left: auto;
    font-size: 18px;
  }

  .perex-2.perex-c.light {
    max-width: none;
    font-size: 24px;
  }

  .perex-2.perex-smaller {
    font-size: 16px;
  }

  .casestudy-slider {
    height: 760px;
  }

  .case-c.case-c__desc {
    margin-right: 2em;
  }

  .case-c.case-c__img {
    -webkit-flex-basis: 80%;
    -ms-flex-preferred-size: 80%;
    flex-basis: 80%;
  }

  .columns-2 {
    margin-right: -15px;
    margin-left: -15px;
  }

  .column-2 {
    padding-right: 16px;
    padding-left: 16px;
  }

  .column-2.col {
    padding-right: 8px;
    padding-left: 8px;
  }

  .hell-p {
    font-size: 16px;
  }

  .hero-img__wrap {
    -webkit-flex-shrink: 1;
    -ms-flex-negative: 1;
    flex-shrink: 1;
  }

  .heading-jumbo-2 {
    margin-bottom: 1em;
    font-size: 42px;
    line-height: 67px;
  }

  .heading-jumbo-2.heading-gradient {
    padding-bottom: 8px;
    font-size: 40px;
  }

  .tags-2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-top: 0em;
    padding-bottom: 4em;
  }

  .tag-2 {
    margin-bottom: 1em;
    text-shadow: none;
  }

  .tag-2:hover {
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
}

@media screen and (max-width: 767px) {
  .heading-jumbo-small {
    font-size: 30px;
    line-height: 52px;
  }

  .rich-text {
    width: 90%;
    max-width: 470px;
    text-align: left;
  }

  .container {
    height: auto;
    margin-top: 0px;
    text-align: left;
  }

  .container.container-header {
    margin-top: 0px;
    padding-top: 60px;
    padding-bottom: 60px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .heading-jumbo {
    margin-bottom: 0.5em;
    font-size: 31px;
    line-height: 50px;
    text-align: left;
  }

  .heading-jumbo.heading-gradient {
    padding-bottom: 16px;
    font-size: 40px;
  }

  .heading-jumbo.heading-gradient.h1-l {
    line-height: 43px;
  }

  .heading-jumbo.heading-error {
    text-align: center;
  }

  .paragraph-tiny.cc-paragraph-tiny-light {
    display: none;
    font-size: 16px;
  }

  .section {
    height: auto;
    margin-right: 0px;
    margin-left: 0px;
    background-position: 100% 0%;
    background-size: contain;
  }

  .section.cc-contact {
    padding: 48px 40px;
  }

  .section.hero {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }

  .button.button-form {
    margin-right: 0em;
  }

  .paragraph-bigger {
    font-size: 16px;
    line-height: 28px;
  }

  .logo-link {
    padding-left: 0px;
  }

  .navigation {
    padding: 20px 30px;
  }

  .intro-wrap {
    display: block;
    width: 90vw;
    height: auto;
    text-align: left;
  }

  .work-experience-grid {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .work-position-wrap {
    margin-bottom: 40px;
  }

  .project-name-link {
    font-size: 16px;
    line-height: 28px;
  }

  .text-field {
    margin-bottom: 5px;
  }

  .text-field.cc-textarea {
    margin-bottom: 5px;
    text-align: left;
  }

  .contact {
    padding-right: 0px;
    padding-left: 0px;
  }

  .contact-form-grid {
    display: block;
    grid-template-areas: "."
      "."
      ".";
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    -ms-grid-rows: auto auto auto auto auto;
    grid-template-rows: auto auto auto auto auto;
  }

  .contact-form {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .contact-form-wrap {
    text-align: left;
  }

  .footer-wrap {
    padding-right: 40px;
    padding-left: 40px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    background-image: none;
    text-align: center;
  }

  .webflow-link.w--current {
    margin-bottom: 40px;
  }

  .footer-links {
    display: block;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
  }

  .footer-item {
    margin-top: 0px;
    margin-bottom: 10px;
    margin-left: 0px;
  }

  .about-head-text-wrap {
    width: 70%;
    max-width: 470px;
  }

  .skills-grid {
    width: 70%;
    max-width: 470px;
  }

  .personal-features-grid {
    width: 70%;
    max-width: 470px;
  }

  .social-media-heading {
    width: 70%;
    max-width: 470px;
  }

  .social-media-grid {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .project-overview-grid {
    width: 80%;
    max-width: 470px;
    margin-top: 90px;
    margin-bottom: 95px;
  }

  .project-description-grid {
    width: 70%;
    max-width: 470px;
    margin-top: 90px;
    margin-bottom: 85px;
  }

  .detail-image {
    margin-bottom: 15px;
  }

  .email-section {
    width: 80%;
    max-width: 470px;
    margin-top: 120px;
    margin-bottom: 120px;
  }

  .email-link {
    font-size: 36px;
    line-height: 54px;
  }

  .utility-page-wrap {
    padding: 15px;
  }

  ._404-wrap {
    padding: 30px;
  }

  .body {
    background-color: #fff;
    background-image: none;
  }

  .button-2.button-mobile {
    display: block;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #62b1ff;
    font-family: itc-avant-garde-gothic-pro, sans-serif;
    color: #180a22;
    font-size: 14px;
    font-weight: 700;
    letter-spacing: 1px;
  }

  .image-2 {
    width: 32px;
    height: 32px;
  }

  .sub-heading {
    text-align: left;
  }

  .text-block-2 {
    font-size: 12px;
    line-height: 18px;
  }

  .block-quote {
    padding: 8px 16px;
    font-size: 21px;
  }

  .section-3 {
    background-color: #fff;
  }

  .section-3.section-testimonails {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .heading-3 {
    font-size: 55px;
    line-height: 70px;
  }

  .paragraph {
    font-size: 21px;
    line-height: 34px;
  }

  .section-p.section-avocado {
    padding-top: 70px;
    padding-bottom: 70px;
  }

  .section-p.section-avocado.desc {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .section-p.section-upwork {
    padding-top: 70px;
    padding-bottom: 0px;
  }

  .section-p.section-how {
    padding-top: 70px;
    padding-bottom: 70px;
  }

  .section-p.section-work-together {
    padding-top: 48px;
    padding-bottom: 48px;
  }

  .section-p.section-bg {
    padding-top: 48px;
    padding-bottom: 48px;
  }

  .section-p.section-light {
    padding-top: 48px;
    padding-bottom: 48px;
  }

  .section-p.casestudy-section {
    margin-top: 0px;
  }

  .h2 {
    margin-bottom: 0.5em;
    font-size: 50px;
    line-height: 1.2;
    text-align: left;
  }

  .h2.h1-smaller {
    font-size: 48px;
    line-height: 53px;
  }

  .h2.h1-good {
    font-size: 48px;
    line-height: 53px;
  }

  .h2.h2--whatdo {
    font-size: 30px;
  }

  .button-cta {
    display: block;
    width: 100%;
  }

  .heading-4 {
    padding-bottom: 0.5em;
    font-size: 51px;
    line-height: 60px;
  }

  .heading-5 {
    text-align: left;
  }

  .company-details {
    margin-top: 40px;
  }

  .div-block-6 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 30px;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    text-align: left;
  }

  .heading-wrap {
    margin-bottom: 16px;
  }

  .perex.perex-alt.perex-trans {
    font-size: 18px;
    line-height: 27px;
  }

  .perex.perex-trans {
    margin-bottom: 48px;
    font-size: 21px;
  }

  .perex.perex-trans.perex-p {
    font-size: 21px;
  }

  .perex.perex-w {
    font-size: 3vw;
  }

  .perex.perex-small {
    font-size: 18px;
    line-height: 27px;
  }

  .perex.perex-sans {
    font-size: 14px;
  }

  .image-7 {
    line-height: 1.3;
  }

  .container-5 {
    padding-right: 0px;
    padding-left: 0px;
  }

  .profile-img {
    width: 256px;
    min-width: 0px;
    margin-right: 16px;
    margin-bottom: 16px;
    padding: 8px;
  }

  .div-block-14 {
    display: block;
    text-align: center;
  }

  .heading-9 {
    margin-bottom: 8px;
    font-size: 24px;
    line-height: 36px;
  }

  .link-block {
    margin-top: 128px;
    margin-bottom: 128px;
  }

  .div-block-15 {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
  }

  .avocado-logo-wrap {
    width: 128px;
    margin-right: 24px;
  }

  .columns {
    padding-right: 0px;
    padding-left: 0px;
  }

  .button-3.button-desktop {
    display: none;
  }

  .section-7 {
    padding-top: 30px;
    padding-bottom: 40px;
    border-width: 30px;
  }

  .block-quote-2 {
    font-size: 31px;
  }

  .heading-11 {
    font-size: 36px;
  }

  .text-block-9 {
    font-size: 24px;
  }

  .italic-text {
    font-size: 26px;
  }

  .service-col {
    padding-right: 0px;
    padding-bottom: 40px;
    padding-left: 0px;
  }

  .service-cols {
    display: block;
  }

  .tags {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  .tag {
    margin-right: 1em;
  }

  .paragraph-tiny-2.cc-paragraph-tiny-light {
    font-size: 16px;
  }

  .navigation-2 {
    padding: 20px 30px;
  }

  .star-2 {
    width: 24px;
  }

  .image-12 {
    width: 40px;
  }

  .container-7 {
    height: auto;
    margin-top: 0px;
    text-align: left;
  }

  .container-7.container-testimonials {
    padding-right: 0px;
    padding-left: 0px;
  }

  .container-7.points {
    padding-top: 20px;
  }

  .blockquote-author-2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .block-quote-3 {
    padding: 8px 16px;
    font-size: 27px;
  }

  .text-block-10 {
    font-size: 12px;
    line-height: 18px;
  }

  .section-p-2.section-me {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .h2-2 {
    font-size: 36px;
    line-height: 43px;
    text-align: left;
  }

  .h2-2.h3.h-col {
    margin-bottom: 0.3em;
  }

  .perex-2 {
    font-size: 16px;
    line-height: 24px;
  }

  .perex-2.perex-smaller {
    margin-bottom: 16px;
  }

  .casestudy {
    display: block;
  }

  .casestudy-slider {
    height: 200vh;
  }

  .case-c.case-c__desc {
    margin-right: 40px;
    margin-bottom: 2em;
  }

  .clients {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: auto;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-align-content: center;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }

  .client-img {
    display: inline-block;
    margin-right: 16px;
    margin-bottom: 64px;
    margin-left: 16px;
  }

  .columns-2 {
    margin-right: 0px;
    margin-left: 0px;
  }

  .column-2 {
    padding-right: 0px;
    padding-bottom: 16px;
    padding-left: 0px;
  }

  .column-2.col {
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
  }

  .hell-p {
    text-align: left;
  }

  .hero-img__wrap {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 auto;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
  }

  .nav-a {
    font-size: 16px;
  }

  .heading-jumbo-2 {
    margin-bottom: 0.5em;
    font-size: 31px;
    line-height: 50px;
    text-align: left;
  }

  .heading-jumbo-2.heading-gradient {
    padding-bottom: 16px;
    font-size: 40px;
  }

  .tags-2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  .tag-2 {
    margin-right: 1em;
  }
}

@media screen and (max-width: 479px) {
  .rich-text {
    width: 100%;
    max-width: none;
  }

  .container {
    padding: 0px 20px;
  }

  .container.container-header {
    padding-top: 20px;
    padding-right: 20px;
    padding-bottom: 96px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .container.container-contact {
    padding-right: 0px;
    padding-left: 0px;
  }

  .container.container-form {
    padding-right: 0px;
    padding-left: 0px;
  }

  .container.grid {
    padding-top: 0px;
    padding-bottom: 48px;
  }

  .heading-jumbo {
    font-size: 36px;
    line-height: 48px;
  }

  .heading-jumbo.heading-gradient {
    font-size: 30px;
  }

  .heading-jumbo.heading-gradient.h1-l {
    font-size: 24px;
    line-height: 28px;
  }

  .heading-jumbo.heading-gradient-2 {
    font-size: 34px;
    line-height: 48px;
  }

  .paragraph-tiny.cc-paragraph-tiny-light {
    display: none;
    font-size: 12px;
  }

  .paragraph-light {
    margin-bottom: 1.5em;
    font-size: 15px;
    line-height: 24px;
    text-align: left;
  }

  .section {
    padding-bottom: 60px;
    background-position: 65% 0%;
    background-size: cover;
  }

  .section.cc-contact {
    padding: 40px 24px 48px;
  }

  .section.hero {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-bottom: 0px;
  }

  .section.section-bg {
    padding-bottom: 80px;
  }

  .button {
    width: 100%;
  }

  .paragraph-bigger.cc-bigger-light {
    font-size: 16px;
  }

  .navigation-items {
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
  }

  .navigation {
    padding-right: 15px;
    padding-left: 5px;
  }

  .menu-button {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 auto;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
  }

  .menu-button.w--open {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 auto;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
  }

  .navigation-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
    -ms-flex-align: end;
    align-items: flex-end;
  }

  .intro-wrap {
    width: 90vw;
    margin-bottom: 1em;
  }

  .text-field {
    padding-left: 20px;
  }

  .contact {
    padding: 0px;
  }

  .contact-headline {
    margin-bottom: 0px;
  }

  .contact-form {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .contact-form-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .footer-wrap {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-right: 20px;
    padding-left: 20px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .webflow-link.w--current {
    margin-bottom: 40px;
  }

  .footer-links {
    display: block;
    margin-right: 0px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .footer-item {
    margin-right: 0px;
  }

  .about-head-text-wrap {
    width: 100%;
    max-width: none;
  }

  .skills-grid {
    width: 100%;
    max-width: none;
  }

  .personal-features-grid {
    width: 100%;
    max-width: none;
  }

  .social-media-heading {
    width: 100%;
    max-width: none;
  }

  .project-overview-grid {
    width: 100%;
    max-width: none;
  }

  .project-description-grid {
    width: 100%;
    max-width: none;
  }

  .email-section {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    max-width: none;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .email-link {
    font-size: 30px;
    line-height: 46px;
  }

  .button-2 {
    font-size: 12px;
  }

  .button-2.button-mobile {
    display: block;
    padding-top: 4px;
    padding-bottom: 4px;
    border-radius: 4px;
    font-family: Montserrat, sans-serif;
    font-size: 13px;
    letter-spacing: 1px;
  }

  .div-block {
    display: block;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
  }

  .image-2 {
    margin-top: 0px;
    margin-right: 0px;
  }

  .sub-heading {
    margin-bottom: 0px;
  }

  .sub-heading.hero-sub {
    font-size: 19px;
  }

  .section-3 {
    padding-top: 40px;
    padding-bottom: 48px;
  }

  .section-3.section-portfolio {
    padding-top: 40px;
  }

  .section-3.section-testimonails {
    padding-top: 0px;
    padding-bottom: 40px;
  }

  .heading-3 {
    font-size: 51px;
    line-height: 66px;
  }

  .paragraph {
    font-size: 19px;
    line-height: 31px;
  }

  .paragraph.paragraph-bigger {
    font-size: 19px;
    line-height: 29px;
  }

  .paragraph.paragraph-bigger.paragraph-2 {
    font-size: 19px;
  }

  .section-p.section-avocado {
    padding: 40px 24px 48px;
  }

  .section-p.section-upwork {
    padding-top: 40px;
    padding-bottom: 0px;
  }

  .section-p.section-how {
    padding-top: 40px;
    padding-bottom: 48px;
  }

  .section-p.section-work-together {
    padding: 40px 24px 48px;
  }

  .section-p.section-light {
    padding-right: 24px;
    padding-left: 24px;
  }

  .section-p.section-temp {
    padding-right: 24px;
    padding-left: 24px;
  }

  .text-span-6 {
    font-size: 19px;
    line-height: 31px;
  }

  .h2 {
    font-size: 31px;
    line-height: 40px;
  }

  .h2.h1-smaller {
    font-size: 31px;
    line-height: 40px;
  }

  .h2.h1-good {
    margin-bottom: 0.618em;
    font-size: 55px;
    line-height: 55px;
  }

  .h2.h1-w {
    padding-top: 2em;
    padding-bottom: 0.25em;
    font-size: 6.5vw;
  }

  .h2.h3.h3-c.h3-light {
    font-size: 14px;
    line-height: 21px;
  }

  .h2.h2--whatdo {
    font-size: 24px;
    font-weight: 300;
  }

  .bold-text.bold-text-2 {
    line-height: 48px;
  }

  .button-cta {
    display: block;
    width: 100%;
    max-width: none;
    margin-right: auto;
    margin-left: auto;
    padding: 24px 10px;
    font-size: 24px;
    font-weight: 700;
    text-align: center;
  }

  .button-cta.w--current {
    text-align: center;
  }

  .heading-4 {
    font-size: 34px;
    line-height: 45px;
  }

  .container-2 {
    padding: 40px 20px;
  }

  .container-3 {
    padding: 40px 20px 0px;
  }

  .container-4 {
    padding: 40px 20px 0px;
  }

  .text-block-6 {
    text-align: left;
  }

  .text-block-6.company-address {
    text-align: left;
  }

  .heading-5 {
    text-align: left;
  }

  .company-details {
    display: block;
    margin-top: 20px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    text-align: left;
  }

  .div-block-6 {
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-align-self: auto;
    -ms-flex-item-align: auto;
    -ms-grid-row-align: auto;
    align-self: auto;
  }

  .paragraph-2 {
    font-size: 19px;
    line-height: 31px;
  }

  .grid {
    -ms-grid-columns: 1fr 1fr;
    grid-template-columns: 1fr 1fr;
    -ms-grid-rows: auto auto auto;
    grid-template-rows: auto auto auto;
  }

  .paragraph-6 {
    font-size: 21px;
    line-height: 34px;
  }

  .paralax {
    display: block;
    width: auto;
  }

  .perex.perex-alt.perex-trans {
    line-height: 29px;
  }

  .perex.perex-trans {
    margin-bottom: 24px;
  }

  .perex.perex-w {
    font-size: 4.5vw;
  }

  .perex.perex-sans {
    text-align: left;
  }

  .div-block-8 {
    padding-right: 20px;
    padding-left: 20px;
  }

  .profile-img {
    width: 240px;
    margin-right: auto;
    margin-left: auto;
  }

  .div-block-14 {
    display: block;
  }

  .column {
    padding-right: 0px;
    padding-bottom: 24px;
    padding-left: 0px;
  }

  .h3 {
    font-size: 24px;
    line-height: 28px;
  }

  .heading-9 {
    margin-bottom: 48px;
    font-size: 20px;
    line-height: 30px;
    text-align: center;
  }

  .link-block {
    display: block;
    text-align: left;
  }

  .avocado-logo-wrap {
    width: 128px;
    margin-right: auto;
    margin-bottom: 16px;
    margin-left: auto;
    text-align: left;
  }

  .bold-text-8 {
    font-size: 30px;
  }

  .button-3 {
    font-size: 12px;
  }

  .button-3.button-desktop {
    display: block;
    padding: 4px 8px;
    font-size: 12px;
  }

  .button-3.button-desktop {
    display: none;
  }

  .social-link {
    width: 20px;
  }

  .social-list {
    margin-top: 20px;
  }

  .social-list.social-list--header {
    display: none;
  }

  .list-item {
    margin-right: 0px;
    margin-left: 12px;
  }

  .section-7 {
    padding-right: 16px;
    padding-left: 16px;
    border-style: none;
  }

  .block-quote-2 {
    padding-right: 0px;
    padding-left: 0px;
    font-size: 24px;
  }

  .heading-11 {
    font-size: 31px;
    line-height: 39px;
  }

  .text-block-9 {
    font-size: 18px;
    line-height: 24px;
  }

  .image-11 {
    margin-top: 30px;
  }

  .italic-text {
    font-size: 18px;
  }

  .service-link {
    padding: 20px;
  }

  .service-p {
    font-size: 21px;
  }

  .list-item-2 {
    margin-left: 8px;
  }

  .social-link-2 {
    width: auto;
  }

  .tags {
    -webkit-justify-content: space-around;
    -ms-flex-pack: distribute;
    justify-content: space-around;
  }

  .tag {
    margin-right: 0.5em;
    margin-left: 0.5em;
  }

  .service-p-2 {
    font-size: 21px;
  }

  .paragraph-tiny-2.cc-paragraph-tiny-light {
    display: none;
    font-size: 12px;
  }

  .navigation-2 {
    padding-right: 15px;
    padding-left: 5px;
  }

  .navigation-wrap-2 {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
    -ms-flex-align: end;
    align-items: flex-end;
  }

  .image-12 {
    width: 40px;
  }

  .container-7 {
    padding: 0px 20px;
  }

  .container-7.container-testimonials {
    padding-right: 0px;
    padding-left: 0px;
  }

  .block-quote-3 {
    font-size: 14px;
    line-height: 21px;
  }

  .section-p-2 {
    padding-top: 80px;
    padding-bottom: 40px;
  }

  .section-p-2.section-me {
    padding-top: 40px;
  }

  .h2-2 {
    margin-bottom: 24px;
    font-size: 31px;
    line-height: 37px;
  }

  .h2-2.h1-c {
    font-size: 27px;
    line-height: 1.3em;
  }

  .h2-2.h1-c.light {
    font-size: 30px;
    line-height: 1.3em;
  }

  .h2-2.h3 {
    font-size: 30px;
    line-height: 1.5em;
  }

  .perex-2 {
    font-size: 16px;
    line-height: 21px;
  }

  .perex-2.perex-c {
    font-size: 14px;
    text-align: left;
  }

  .perex-2.perex-c.light {
    font-size: 19px;
  }

  .perex-2.perex-smaller {
    font-size: 14px;
    line-height: 21px;
  }

  .case-c.case-c__desc {
    margin-right: 20px;
    margin-left: 20px;
  }

  .clients {
    margin-top: 24px;
    -webkit-transform: scale(0.8);
    -ms-transform: scale(0.8);
    transform: scale(0.8);
  }

  .client-img {
    margin-bottom: 64px;
  }

  .columns-2 {
    margin-right: 0px;
    margin-left: 0px;
  }

  .column-2 {
    padding-right: 0px;
    padding-bottom: 16px;
    padding-left: 0px;
  }

  .heading-jumbo-2 {
    font-size: 36px;
    line-height: 48px;
  }

  .heading-jumbo-2.heading-gradient {
    font-size: 30px;
  }

  .tags-2 {
    -webkit-justify-content: space-around;
    -ms-flex-pack: distribute;
    justify-content: space-around;
  }

  .tag-2 {
    margin-right: 0.5em;
    margin-left: 0.5em;
  }
}

#w-node-_302daff0-b07e-63a7-595f-8901429ba655-1d954aa0 {
  -ms-grid-column: 2;
  grid-column-start: 2;
  -ms-grid-column-span: 1;
  grid-column-end: 3;
  -ms-grid-row: 1;
  grid-row-start: 1;
  -ms-grid-row-span: 1;
  grid-row-end: 2;
}

#w-node-b173b21d-1e23-7203-0689-a6f2a70378f1-1d954aa0 {
  -ms-grid-column: 1;
  grid-column-start: 1;
  -ms-grid-column-span: 1;
  grid-column-end: 2;
  -ms-grid-row: 2;
  grid-row-start: 2;
  -ms-grid-row-span: 1;
  grid-row-end: 3;
}

#Message.w-node-_2b0ec9e1-c8c4-8d44-ce10-940d49b40f6f-c8c751e7 {
  -ms-grid-row: span 1;
  grid-row-start: span 1;
  -ms-grid-row-span: 1;
  grid-row-end: span 1;
  -ms-grid-column: span 2;
  grid-column-start: span 2;
  -ms-grid-column-span: 2;
  grid-column-end: span 2;
}

#w-node-_83aaac71-ce7f-5ae6-3908-85ca89e4163d-c8c751e7 {
  -ms-grid-row: 7;
  -ms-grid-column: 3;
  grid-area: Area;
}

@font-face {
  font-family: 'Font awesome brands';
  src: url('../fonts/fa-brands-400.eot') format('embedded-opentype'), url('../fonts/fa-brands-400.woff') format('woff'), url('../fonts/fa-brands-400.ttf') format('truetype'), url('../fonts/fa-brands-400.svg') format('svg');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Font awesome 5 pro 300';
  src: url('../fonts/Font-Awesome-5-Pro-Light-300.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: auto;
}